<cfcomponent output="false">
	
	<cffunction name="doSubscribe" access="package" output="false" returntype="string">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfset arguments.event.paramValue('subAction','')>
			<cfset arguments.event.paramValue('mid','0')>
			<cfset local.puid = arguments.event.getValue('puid','')>
			<cfset local.dspStep = "">
			<cfset local.showTreePrices = false>
			<cfset local.termDateString = "">
			<cfset local.prevTermDateString = "">
			<cfset local.maxFrequencyInstallments = getMaxFrequencyInstallments(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

			<!--- load any subxml in progress --->
			<cfset local.xmlSubscribeMember = loadSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'))>

			<!--- update pointer if necessary --->
			<cfif len(local.puid) and local.puid neq xmlSearch(local.xmlSubscribeMember,"string(//process/@pointer)")>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.pointer = local.puid>
			</cfif>

			<cfif XMLSearch(local.xmlSubscribeMember,"count(//set[@id='0']/subscription)") gt 0>
				<cfset local.subTermStartDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
				<cfset local.subTermEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>
				
				<cfif (len(local.subTermStartDate) eq 0) OR (len(local.subTermEndDate) eq 0)>
					<cfset local.termDateRFID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rfid)")>
					<cfset local.subTermFlag = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@termflag)")>
					
					<cfset local.subTermDates = getSubTermDates(termDateRFID=local.termDateRFID, subTermFlag=local.subTermFlag)>
					<cfif local.subTermDates.success eq true>
						<cfset local.subTermStartDate = local.subTermDates.subTermStartDate>
						<cfset local.subTermEndDate = local.subTermDates.subTermEndDate>
					</cfif>
				</cfif>

				<cfif (len(local.subTermStartDate) neq 0) AND (len(local.subTermEndDate) neq 0)>
					<cfset local.termDateString = "#DateFormat(local.subTermStartDate, 'm/d/yyyy')# - #DateFormat(local.subTermEndDate, 'm/d/yyyy')#">
				</cfif>
				<cfset local.prevSubID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@sid)"))>
				<cfset local.prevTermDates = getPrevSubTermDates(memberID=arguments.event.getValue('mid'), subscriberID=local.prevSubID)>			
				<cfif (local.prevTermDates.success eq true) AND (len(local.prevTermDates.subTermStartDate)) AND (len(local.prevTermDates.subTermEndDate))>
					<cfset local.prevTermDateString = "#DateFormat(local.prevTermDates.subTermStartDate, 'm/d/yyyy')# - #DateFormat(local.prevTermDates.subTermEndDate, 'm/d/yyyy')#">
				</cfif>
			</cfif>

			<cfswitch expression="#arguments.event.getValue('subAction')#">
				<cfcase value="chooseSub">
					<cfset local.pointerNode = xmlSearch(local.xmlSubscribeMember,"//node()[@uid = '#local.puid#']")>
					<cfset local.memberID = local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid>
	
					<!--- if pointer is the first set (set with id=0) and there is no addonID, adding a root subscription --->
					<cfif arraylen(local.pointerNode) and local.pointerNode[1].xmlName eq "set" and local.pointerNode[1].xmlAttributes.id is 0 and NOT arguments.event.valueExists('aoid')>
						<cfquery name="local.qrySubName" datasource="#application.dsn.membercentral.dsn#">
							select top 1 s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.allowRateGLAccountOverride,
								s.paymentOrder as subPayOrder, o.subActivationCode, o2.subActivationCode as subAlternateActivationCode
							from dbo.sub_subscriptions s
							inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
							inner join dbo.sub_activationOptions o2 on o2.subActivationID = s.subAlternateActivationID
							where s.subscriptionID = <cfqueryparam value="#val(arguments.event.getValue('subid',0))#" cfsqltype="CF_SQL_INTEGER">
							and s.soldSeparately = 1
							and s.status = 'A'
						</cfquery>
	
						<cfif local.qrySubName.recordcount is 0>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&err=CS.NS" addtoken="no">
						<cfelseif xmlSearch(local.pointerNode[1],"count(//subscription[@id = '#local.qrySubName.subscriptionID#'])") gt 0>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showRates&puid=#local.puid#" addtoken="no">
						<cfelse>
							<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.puid, subid=local.qrySubName.subscriptionID, 
								subname=local.qrySubName.subscriptionName, subTermFlag=local.qrySubName.rateTermDateFlag, GLAccountIDToUse=local.qrySubName.GLAccountID, 
								allowRateGLOverride=local.qrySubName.allowRateGLAccountOverride, payOrder=local.qrySubName.subPayOrder,
								activationOptionCode=local.qrySubName.subActivationCode, alternateActivationOptionCode=local.qrySubName.subAlternateActivationCode)>
							<cfif len(local.strAddSubResult.subUID)>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.memberID, subXML=toString(local.strAddSubResult.subXML))>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showRates&puid=#local.strAddSubResult.subUID#" addtoken="no">
							<cfelse>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&err=CS.NAS" addtoken="no">
							</cfif>
						</cfif>
	
					<!--- else if pointer is a sub and there is an addonID --->
					<cfelseif arraylen(local.pointerNode) and local.pointerNode[1].xmlName eq "subscription" and arguments.event.valueExists('aoid')>
						<cfquery name="local.qrySubName" datasource="#application.dsn.membercentral.dsn#">
							select ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.allowRateGLAccountOverride,
								o.subActivationCode, o2.subActivationCode as subAlternateActivationCode, sets.setid, sets.setName,
								ao.useAcctCodeInSet, ao.PCnum, ao.PCPctOffEach, s.paymentOrder as subPayOrder
							from dbo.sub_addons as ao
							inner join dbo.sub_sets as sets on sets.setID = ao.childSetID
							INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
							INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID and s.status = 'A'
							inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
							inner join dbo.sub_activationOptions o2 on o2.subActivationID = s.subAlternateActivationID
							where ao.addOnID = <cfqueryparam value="#val(arguments.event.getValue('aoid',0))#" cfsqltype="CF_SQL_INTEGER">
							and s.subscriptionID = <cfqueryparam value="#val(arguments.event.getValue('subid',0))#" cfsqltype="CF_SQL_INTEGER">
							and ao.subscriptionID = <cfqueryparam value="#val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@id)"))#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
					
						<cfif local.qrySubName.recordcount is 0>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&err=CS.NSAO" addtoken="no">
						<cfelse>
							<cfset local.strAddSetResult = addSet(subXML=local.xmlSubscribeMember, parentUID=local.puid, setid=local.qrySubName.setid, setname=local.qrySubName.setname)>
							<cfif len(local.strAddSetResult.setUID)>
								<cfset local.xmlSubscribeMember = local.strAddSetResult.subXML>

								<!--- get the number of free subs added to determine if this one is free --->
								<cfset local.subFreeCount = XMLSearch(local.xmlSubscribeMember,"count(//subscription[@uid='#local.puid#']/set[@uid='#local.strAddSetResult.setUID#']/subscription[@pcisfree='true'])")>	
								<cfif local.qrySubName.PCNum gt local.subFreeCount>
									<cfset local.currSubIsFree = true>
								<cfelse>
									<cfset local.currSubIsFree = false>
								</cfif>
								
								<cfif local.qrySubName.useAcctCodeInSet eq 0>
									<!--- use the parent GLAccountID --->
									<cfset local.currGLAToUse = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@glaccountidtouse)"))>
								<cfelse>
									<cfset local.currGLAToUse = local.qrySubName.GLAccountID>
								</cfif>
								
								<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.strAddSetResult.setUID, subid=local.qrySubName.subscriptionID, 
									subname=local.qrySubName.subscriptionName, subTermFlag=local.qrySubName.rateTermDateFlag, GLAccountIDToUse=local.currGLAToUse,
									allowRateGLOverride=local.qrySubName.allowRateGLAccountOverride, pcnumfree=local.qrySubName.PCnum, 
									pcpctoff=local.qrySubName.PCPctOffEach, pcfree=local.currSubIsFree, payOrder=local.qrySubName.subPayOrder, 
									activationOptionCode=local.qrySubName.subActivationCode, alternateActivationOptionCode=local.qrySubName.subAlternateActivationCode)>
								<cfif len(local.strAddSubResult.subUID)>
									<cfset local.strUpdateFreeSubsResult = updateFreeSubs(subXML=local.strAddSubResult.subXML, setUID=local.strAddSetResult.setUID, setParentUID=local.puid)>
									<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.memberID, subXML=toString(local.strUpdateFreeSubsResult.subXML))>
									<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showRates&puid=#local.strAddSubResult.subUID#" addtoken="no">
								<cfelse>
									<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&err=CS.NAS2" addtoken="no">
								</cfif>
							<cfelse>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&err=CS.NASS" addtoken="no">
							</cfif>
						</cfif>
	
					<!--- else no bueno --->
					<cfelse>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&err=CS.NP" addtoken="no">
					</cfif>
				</cfcase>

				<cfcase value="showRates">
					<cfif xmlsearch(local.xmlSubscribeMember,"count(//set/subscription[@uid = '#local.puid#'])") gt 0>
						<!--- if this sub uses rates (an addon setting), get them --->
						<cfset local.topUID = XMLSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@uid)")>
						<cfset local.origFreqID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@freqid)"))>
						<cfset local.subUseRates = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@userates)")>
						
						<cfset local.qryRates = getRateQuery(memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid,
							siteID=arguments.event.getValue('mc_siteinfo.siteid'), isRoot=(local.topUID eq local.puid),
							subID=val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@id)")),
							currFreqID=val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@freqid)")),
							currRateID=val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@rateID)")),
							currRFID=val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@rfid)")),
							isRenewal=false, autoSelectRateMatch=false)>

						<!--- if only one rate, auto select it (do what chooseRate does) --->
						<cfif local.qryRates.recordcount eq 1>
							<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.puid, rfid=local.qryRates.rfid)>
							<cfif local.topUID eq local.puid>
								<cfset local.strUpdateRateResult = updateRates(subXML=local.strAddRateResult.subXML, origFreqID=local.origFreqID, 
									isRenewal=false, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, subXML=toString(local.strUpdateRateResult.subXML))>
							<cfelse>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, subXML=toString(local.strAddRateResult.subXML))>
							</cfif>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.puid#" addtoken="no">
						<cfelse>
							<cfset local.subPCPctOff = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@pcpctoff)")>
							<cfset local.subPCRateAmt = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@pcrateamt)")>
							<cfset local.subPCFree = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@pcisfree)")>
							<cfset local.subName = xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@name)")>
							<cfset local.currSetID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/../@id)")>
							<cfset local.dspStep = "subRates">
						</cfif>
					<cfelse>
						<cfset local.dspStep = "invalidData">
					</cfif>
				</cfcase>
				
				<cfcase value="chooseRate">
					<cfset local.topUID = XMLSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@uid)")>
					<cfset local.origFreqID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@freqid)"))>
					<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.puid, rfid=val(arguments.event.getValue('rfid',0)))>
					<cfif local.topUID eq local.puid>
						<cfset local.strUpdateRateResult = updateRates(subXML=local.strAddRateResult.subXML, origFreqID=local.origFreqID, 
							isRenewal=false, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.strUpdateRateResult.subXML))>
					<cfelse>
						<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.strAddRateResult.subXML))>
					</cfif>
					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.puid#" addtoken="no">
				</cfcase>

				<cfcase value="showAddons">
					<cfset local.chkRates = checkRates(subXML=local.xmlSubscribeMember)>
					<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

					<cfquery name="local.qryAddOnsWithSubs" datasource="#application.dsn.membercentral.dsn#">
						set nocount on;
						
						declare @FID int, @memberid int, @siteID int;
						select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
						select @siteid = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;
						select @memberid = <cfqueryparam value="#local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid#" cfsqltype="CF_SQL_INTEGER">;
						
						select ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.allowRateGLAccountOverride,
						o.subActivationCode, o2.subActivationCode as subAlternateActivationCode,
						sets.setid, sets.setName, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, 
						ao.PCnum, ao.PCPctOffEach, s.paymentOrder as subPayOrder
						from dbo.sub_addons as ao
						inner join dbo.sub_sets as sets on sets.setID = ao.childSetID
						INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
						INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID and s.status = 'A'
						INNER JOIN dbo.sub_activationOptions as o on o.subActivationID = s.subActivationID
						INNER JOIN dbo.sub_activationOptions as o2 on o2.subActivationID = s.subAlternateActivationID
						inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
						inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.isRenewalRate = 0
							and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID =  @siteid
							and srfrp.siteResourceID = r.siteResourceID
						    AND srfrp.functionID = @FID
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteid
							and srfrp.rightPrintID = gprp.rightPrintID
						inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
						    and m.memberID = @memberID						
						left outer join dbo.sub_subscribers sr 
							inner join dbo.sub_statuses st on st.statusID = sr.statusID and st.statusCode = 'A'
							on sr.subscriptionID = s.subscriptionID and sr.memberID = @memberID
						where ao.subscriptionID = <cfqueryparam value="#val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@id)"))#" cfsqltype="CF_SQL_INTEGER">
						group by ao.orderNum, ss.orderNum, ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.allowRateGLAccountOverride,
							o.subActivationCode, o2.subActivationCode, sets.setid, sets.setName, ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.PCnum,
							ao.PCPctOffEach, s.paymentOrder
						order by ao.orderNum, ss.orderNum;
					</cfquery>
					<cfquery name="local.qryAddOns" dbtype="query">
						select addOnID, setid, setname, minAllowed, maxAllowed, count(subscriptionid) as SubCount
						from [local].qryAddOnsWithSubs
						group by addOnID, setid, setname, minAllowed, maxAllowed
					</cfquery>
					
					<!--- if no add ons, go back to parent sub (same as doneWithAddOns) --->
					<cfif local.qryAddOnsWithSubs.recordcount is 0>
						<cfset local.parentSubUID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[set/subscription[@uid = '#local.puid#']]/@uid)")>
						<cfif len(local.parentSubUID)>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.parentSubUID#" addtoken="no">
						<cfelse>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showConfirm" addtoken="no">
						</cfif>
					</cfif>
					
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingSubs">
						select s.subscriptionID
						from dbo.sub_subscribers s
						inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
						inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
						inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode = 'A'
						where memberID = <cfqueryparam value="#local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid#" cfsqltype="CF_SQL_INTEGER">
						and s.subEndDate > getDate()
					</cfquery>
					<cfset local.listSubscribed = valueList(local.qryExistingSubs.subscriptionID)>				
					
					<!--- If minAllowed = maxAllowed = num of subs in set then auto add them --->
					<cfloop query="local.qryAddOns">
						<cfif local.qryAddOns.minAllowed is local.qryAddOns.maxAllowed and local.qryAddOns.maxAllowed gte local.qryAddOns.SubCount>
							<cfset local.strAddSetResult = addSet(subXML=local.xmlSubscribeMember, parentUID=local.puid, setid=local.qryAddOns.setid, setname=local.qryAddOns.setname)>
							<cfif len(local.strAddSetResult.setUID)>
								<cfset local.xmlSubscribeMember = local.strAddSetResult.subXML>
								<cfloop query="local.qryAddOnsWithSubs">
									<cfif local.qryAddOns.addOnID is local.qryAddOnsWithSubs.addOnId>
										<!--- get the number of free subs added to determine if this one is free --->
										<cfset local.subFreeCount = XMLSearch(local.xmlSubscribeMember,"count(//subscription[@uid='#local.puid#']/set[@uid='#local.strAddSetResult.setUID#']/subscription[@pcisfree='true'])")>	
										<cfif local.qryAddOnsWithSubs.PCNum gt local.subFreeCount>
											<cfset local.currSubIsFree = true>
										<cfelse>
											<cfset local.currSubIsFree = false>
										</cfif>
	
										<cfif local.qryAddOnsWithSubs.useAcctCodeInSet eq 0>
											<!--- use the parent GLAccountID --->
											<cfset local.currGLAToUse = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@glaccountidtouse)"))>
										<cfelse>
											<cfset local.currGLAToUse = local.qryAddOnsWithSubs.GLAccountID>
										</cfif>
	
										<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.strAddSetResult.setUID, 
											subid=local.qryAddOnsWithSubs.subscriptionID, subname=local.qryAddOnsWithSubs.subscriptionName, 
											subTermFlag=local.qryAddOnsWithSubs.rateTermDateFlag, GLAccountIDToUse=local.currGLAToUse, 
											allowRateGLOverride=local.qryAddOnsWithSubs.allowRateGLAccountOverride, pcnumfree=local.qryAddOnsWithSubs.PCnum,
											pcpctoff=local.qryAddOnsWithSubs.PCPctOffEach, pcfree=local.currSubIsFree, 
											payOrder=local.qryAddOnsWithSubs.subPayOrder, activationOptionCode=local.qryAddOnsWithSubs.subActivationCode, 
											alternateActivationOptionCode=local.qryAddOnsWithSubs.subAlternateActivationCode)>
										<cfset local.xmlSubscribeMember = local.strAddSubResult.subXML>
									</cfif>
								</cfloop>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.xmlSubscribeMember))>
							</cfif>
						</cfif>
					</cfloop>
					
					<cfset local.subName = xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@name)")>
					<cfset local.dspStep = "SubAddons">
				</cfcase>

				<cfcase value="removeSub">
					<cfset local.subUID = arguments.event.getValue('subuid','')>	
	
					<cfset local.setUID = XMLSearch(local.xmlSubscribeMember,"string(//subscription[@uid='#local.subUID#']/../@uid)")>
					<cfset local.setParentUID = XMLSearch(local.xmlSubscribeMember,"string(//subscription[@uid='#local.subUID#']/../../@uid)")>
	
					<cfset local.strRemoveSubResult = removeSub(subXML=local.xmlSubscribeMember, subUID=local.subUID)>
					<cfset local.strUpdateFreeSubsResult = updateFreeSubs(subXML=local.strRemoveSubResult.subXML, setUID=local.setUID, setParentUID=local.setParentUID)>
					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.strUpdateFreeSubsResult.subXML))>

					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&removeSub=1" addtoken="no">
				</cfcase>

				<cfcase value="doneWithAddOns">
					<cfset local.parentSubUID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[set/subscription[@uid = '#local.puid#']]/@uid)")>
					<cfif len(local.parentSubUID)>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.parentSubUID#" addtoken="no">
					<cfelse>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showConfirm" addtoken="no">
					</cfif>
				</cfcase>

				<cfcase value="changePrices">
					<cfset local.subTreeVerboseWithPrices = getSubTreeVerboseWithPrices(subXML=local.xmlSubscribeMember)>
					<cfset local.dspStep = "changePrices">
				</cfcase>

				<cfcase value="saveNewPrices">
					<cfloop array="#XMLSearch(local.xmlSubscribeMember,'//subscription')#" index="local.thisSub">
						<cfset local.subIDToUse = Replace(local.thisSub.xmlAttributes.uid, "-", "_", "all")>
	
						<cfset local.changePriceNew = val(ReReplace(arguments.event.getValue("newRate_#local.subIDToUse#","0"),'[^0-9\.]','','ALL'))>
						<cfset local.changePriceOrig = val(ReReplace(arguments.event.getValue("origRate_#local.subIDToUse#","0"),'[^0-9\.]','','ALL'))>
	
						<cfif local.changePriceNew neq local.changePriceOrig>
							<cfset local.thisSub.xmlAttributes.userates = 0>
							<cfset local.thisSub.xmlAttributes.pricechanged = 1>
							<cfset local.thisSub.xmlAttributes.pcrateamt = local.changePriceNew>
						</cfif>
					</cfloop>

					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.xmlSubscribeMember))>

					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showConfirm" addtoken="no">
				</cfcase>

				<cfcase value="changeDates">
					<cfset local.subStartDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
					<cfset local.subEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>
					<cfset local.subGraceEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@graceEndDateOverride)")>
					
					<cfif (len(local.subStartDate) eq 0) OR (len(local.subEndDate) eq 0)>
						<cfset local.termDateRFID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rfid)")>
						<cfset local.subTermFlag = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@termflag)")>
						
						<cfset local.subTermDatesCD = getSubTermDates(termDateRFID=local.termDateRFID, subTermFlag=local.subTermFlag)>
						<cfif local.subTermDatesCD.success eq true>
							<cfset local.subStartDate = local.subTermDatesCD.subTermStartDate>
							<cfset local.subEndDate = local.subTermDatesCD.subTermEndDate>
							<cfset local.subGraceEndDate = local.subTermDatesCD.subTermGraceEndDate>
						</cfif>
					</cfif>
					
					<cfset local.dspStep = "changeDates">
				</cfcase>

				<cfcase value="saveNewDates">
					<cfset local.newStartDate = arguments.event.getValue("fTermFrom","")>
					<cfset local.newEndDate = arguments.event.getValue("fTermTo","")>
					<cfset local.newGraceEndDate = arguments.event.getValue("fTermGrace","")>
					
					<cfif (Len(local.newStartDate)) gt 0 AND (Len(local.newEndDate) gt 0)>
						<cfset local.subNode = xmlSearch(local.xmlSubscribeMember,"//set[@id='0']/subscription")>
						<cfset local.subNode[1].xmlAttributes.startDateOverride = local.newStartDate>
						<cfset local.subNode[1].xmlAttributes.endDateOverride = local.newEndDate>
						<cfset local.subNode[1].xmlAttributes.graceEndDateOverride = local.newGraceEndDate>
					</cfif>

					<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
						<cfset local.arrSubs = xmlsearch(local.xmlSubscribeMember,"//set/subscription")>
						<cfif arrayLen(local.arrSubs)>
							<cfloop array="#local.arrSubs#" index="local.thisSubRow">
								<cfset local.tmpUIDNoDash = replace(local.thisSubRow.xmlAttributes.uid,"-","","ALL")>
								<cfif (local.thisSubRow.xmlAttributes.currstatus eq 'D') OR (StructKeyExists(local.thisSubRow.xmlAttributes,"deleteme") AND local.thisSubRow.xmlAttributes.deleteme eq 1)>
								<cfelseif len(arguments.event.getValue("fRecogFrom#local.tmpUIDNoDash#","")) and len(arguments.event.getValue("fRecogTo#local.tmpUIDNoDash#",""))>
									<cfset local.thisSubRow.xmlAttributes.recogStartDate = arguments.event.getValue("fRecogFrom#local.tmpUIDNoDash#")>
									<cfset local.thisSubRow.xmlAttributes.recogEndDate = arguments.event.getValue("fRecogTo#local.tmpUIDNoDash#")>
								</cfif>
							</cfloop>
						</cfif>
					</cfif>
							
					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.xmlSubscribeMember))>

					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.puid#" addtoken="no">
				</cfcase>

				<cfcase value="showConfirm">
					<cfset local.memberID = local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid>
					
					<cfset local.subStartOverrideDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
					<cfset local.subEndOverrideDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>
					<cfset local.dateNow = DateFormat(now(), "mm/dd/yyyy")>
					<cfset local.acceptDate = ''>
					<cfif (len(local.subStartOverrideDate) neq 0) OR (len(local.subEndOverrideDate) neq 0)>
						<cfset local.subStartCompare = DateCompare(local.subStartOverrideDate, local.dateNow)>
						<cfset local.subEndCompare = DateCompare(local.subEndOverrideDate, local.dateNow)>
						<cfif local.subEndCompare lte 0>
							<cfset local.acceptDate = local.subStartOverrideDate>
						<cfelse>
							<cfset local.acceptDate = ''>
						</cfif>
					</cfif>				
					
					<cfset local.arrPaySchedule = arrayNew(1)>
					<cfset local.strPS = { date='', amount='' }>
					<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
						<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
					</cfloop>
					<cfset local.chkRates = checkRates(subXML=local.xmlSubscribeMember)>
					
					<cfset local.amountToCharge = 0>
					<cfset local.firstPaymentMinimum = 0>
					<cfset local.numPaymentsToUse = 0>
	
					<cfset local.termDateRFID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rfid)")>
					<cfset local.subTermFlag = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@termflag)")>
					<cfset local.rootNumPayments = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rateInstallments)")>
					<cfset local.rootRateInterval = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rateInterval)")>
					<cfset local.rootFreqID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@freqid)")>

					<cfif (len(local.subStartOverrideDate) neq 0) OR (len(local.subEndOverrideDate) neq 0)>
						<cfset local.subPaymentDates = getSubPaymentDates(local.subStartOverrideDate,local.subEndOverrideDate,local.rootNumPayments,local.rootRateInterval)>

					<cfelseif structKeyExists(local,"subStartDate") and structKeyExists(local,"subEndDate") and isDate(local.subStartDate) and isDate(local.subEndDate)>
						<cfset local.subPaymentDates = getSubPaymentDates(local.subStartDate, local.subEndDate, local.rootNumPayments, local.rootRateInterval)>

					<cfelse>				
						<cfset local.subTermDates = getSubTermDates(termDateRFID=local.termDateRFID, subTermFlag=local.subTermFlag)>
						<cfset local.subPaymentDates = getSubPaymentDates(local.subTermDates.subTermStartDate, local.subTermDates.subTermEndDate, local.rootNumPayments, local.rootRateInterval)>
					</cfif>

					<cfset local.numPaymentsToUse = val(local.subPaymentDates.numPayments)>
					<cfset local.subAmts = getSubscriptionAmounts(subXML=local.xmlSubscribeMember, numPaymentsToUse=local.numPaymentsToUse)>
					<cfset local.amountToCharge = local.subAmts.qryTotals.totalAmt>
					
					<cfif local.subAmts.qryUpFrontAmt.totalAmt gt 0>
						<cfset local.arrPaySchedule[1] = { date=now(), amount=numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__") }>
						<cfset local.firstPaymentMinimum = numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__")>
					</cfif>
					<cfset local.distribAmt = local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse>
					<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.subAmts.qryNonUpFrontAmt.totalAmt - (numberformat((local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>
					<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
						<cfset local.loopAmt = 0>
						<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
						<cfset local.arrPaySchedule[local.loopCnt] = { date=now(), amount=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
					</cfloop>
					<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >
					
					<cfloop from="1" to="#ArrayLen(local.subPaymentDates.arrDates)#" index="local.thisP">
						<cfset local.arrPaySchedule[local.thisP].date = local.subPaymentDates.arrDates[local.thisP]>
					</cfloop>
				
					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.memberID, subXML=toString(local.xmlSubscribeMember))>

					<cfset local.dspStep = "confirm">
				</cfcase>

				<cfcase value="doConfirm">
					<cfset local.retStruct = doConfirm(subXML=local.xmlSubscribeMember, strEventCollection=arguments.event.getCollection())>
					<cfif local.retStruct.success eq 0>
						<cfset local.errMsg = "We were unable to add this subscription.">
						<cfset local.dspStep = "confirmError">
					<cfelse>
						<cfset local.dspStep = "confirmDone">
					</cfif>
				</cfcase>

				<cfdefaultcase>
					<cfset local.xmlSubscribeMember = resetSubXML(memberID=arguments.event.getValue('mid'))>
					<cfset local.puid = xmlSearch(local.xmlSubscribeMember,"string(//process/@pointer)")>
					<cfset local.dspStep = "initialSubs">
				</cfdefaultcase>
			</cfswitch>

			<!--- get member info --->
			<cfset local.qryMember = CreateObject("component","model.admin.members.members").getMember_demo(memberid=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid)>
	
			<!--- no member found --->
			<cfif val(local.qryMember.memberid) is 0> 
				<cfsavecontent variable="local.data">
					<cfoutput>That member could not be found.</cfoutput>
				</cfsavecontent>
	
			<!--- good member --->
			<cfelse>
				<cfif listFindNoCase("subRates,subAddons,confirm",local.dspStep)>
					<cfset local.subTreeVerbose = getSubTreeVerbose(subXML=local.xmlSubscribeMember, siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
						termDateString=local.termDateString, useAccrualAccounting=arguments.event.getValue('mc_siteInfo.useAccrualAcct'))>
				</cfif>
				<cfif listFindNoCase("confirm",local.dspStep)>
					<cfset local.qryTaxStateZIP = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.qryMember.memberid)>
				</cfif>
				
				<cfsavecontent variable="local.data">
					<cfinclude template="dsp_subscribe.cfm">
				</cfsavecontent>			
			</cfif>

		<cfcatch type="any">
			<cfset local.arguments = duplicate(arguments)>
			<cfset structDelete(local.arguments,"event")>
			<cfset local.arguments.event = arguments.event.getCollection()>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="model.admin.subscriptions.subscriptionReg.doSubscribe error + rethrow")>
			<cfrethrow>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doConfirm" access="private" output="no" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="strEventCollection" type="struct" required="true">
		<cfargument name="bypassQueue" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset var errorStruct = structNew()>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>

		<cfparam name="arguments.strEventCollection.mc_siteInfo.siteID" default="0">
		<cfset local.maxFrequencyInstallments = getMaxFrequencyInstallments(siteID=arguments.strEventCollection.mc_siteInfo.siteID)>

		<cftry>
			<cfset local.subMemberID = int(val(arguments.subXML.xmlRoot.process.xmlAttributes.memberid))>
			<cfif val(session.cfcuser.memberdata.memberID) eq 0>
				<cfset local.actorMemberID = local.subMemberID>
			<cfelse>
				<cfset local.actorMemberID = session.cfcuser.memberdata.memberID>
			</cfif>
			
			<cfparam name="arguments.strEventCollection.mid" default="0">
			<cfparam name="arguments.strEventCollection.sid" default="0">
			<cfparam name="arguments.strEventCollection.currAction" default="">
			<cfparam name="arguments.strEventCollection.invOnly" default="false">
			<cfparam name="arguments.strEventCollection.updateActiveDate" default="">
			<cfparam name="arguments.strEventCollection.vSub" default="">
			<cfparam name="arguments.strEventCollection.startDateOverride" default="">
			<cfparam name="arguments.strEventCollection.saleDateOverride" default="">
			<cfparam name="arguments.strEventCollection.chkBilled" default="0">
			<cfparam name="arguments.strEventCollection.chkRenewed" default="0">
			<cfparam name="arguments.strEventCollection.skipEmailTemplateNotifications" default="0">
			<cfparam name="arguments.strEventCollection.hInvoiceProfileIDs" default="0">
			<cfparam name="arguments.strEventCollection.ps_upfront_amt" default="0">
			<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
				<cfparam name="arguments.strEventCollection['ps_#local.thisP#_amt']" default="0">
				<cfparam name="arguments.strEventCollection['ps_#local.thisP#_hasiid']" default="0">
				<cfparam name="arguments.strEventCollection['ps_#local.thisP#_date']" default="">
				<cfloop list="#arguments.strEventCollection.hInvoiceProfileIDs#" index="local.thisInvProfileID">
					<cfparam name="arguments.strEventCollection['hps_#local.thisP#_#local.thisInvProfileID#_appearOnThisInvoice']" default="false">
					<cfparam name="arguments.strEventCollection['hps_#local.thisP#_#local.thisInvProfileID#_amt']" default="0">
					<cfparam name="arguments.strEventCollection['ps_#local.thisP#_#local.thisInvProfileID#_iid']" default="0">
				</cfloop>
			</cfloop>
			<cfparam name="arguments.strEventCollection.stateIDForTax" default="0">
			<cfparam name="arguments.strEventCollection.zipForTax" default="">

			<cfset local.subscriberID = arguments.strEventCollection.sid>
			<cfset local.currAction = arguments.strEventCollection.currAction>
			<cfset local.invoicesOnly = arguments.strEventCollection.invOnly>
			<cfset local.updActiveDate = arguments.strEventCollection.updateActiveDate>
			<cfset local.vSub = arguments.strEventCollection.vSub>
			<cfset local.overrideStartDate = arguments.strEventCollection.startDateOverride>
			<cfset local.overrideSaleDate = arguments.strEventCollection.saleDateOverride>
			<cfset local.newAsBilled = arguments.strEventCollection.chkBilled>
			<cfset local.newAsRenewed = arguments.strEventCollection.chkRenewed>
			<cfset local.skipEmailTemplateNotifications = arguments.strEventCollection.skipEmailTemplateNotifications>
			<cfset local.listInvoiceProfileIDs = arguments.strEventCollection.hInvoiceProfileIDs>
			<cfset local.ttlUpfrontAmt = val(arguments.strEventCollection.ps_upfront_amt)>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
				select subStartDate, subEndDate, graceEndDate
				from dbo.sub_subscribers
				where subscriberID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.subscriberID#">
			</cfquery>
			<cfset local.subStartDate = local.qrySubDates.subStartDate>
			<cfset local.subEndDate = local.qrySubDates.subEndDate>
			<cfset local.graceEndDate = local.qrySubDates.graceEndDate>

			<cfif StructKeyExists(arguments.subXML.xmlRoot.process.xmlAttributes,"payProfileID")>
				<cfset local.invPayProfileID = arguments.subXML.xmlRoot.process.xmlAttributes.payProfileID>
			<cfelse>	
				<cfset local.invPayProfileID = 0>
			</cfif>

			<cfif local.invPayProfileID GT 0 AND StructKeyExists(arguments.subXML.xmlRoot.process.xmlAttributes,"profileID")>
				<cfset local.invMerchantProfileID = val(arguments.subXML.xmlRoot.process.xmlAttributes.profileID)>
			<cfelse>
				<cfset local.invMerchantProfileID = 0>
			</cfif>

			<cfif local.invPayProfileID GT 0 AND StructKeyExists(arguments.subXML.xmlRoot.process.xmlAttributes,"payProcessFee")>
				<cfset local.invPayProcessFee = val(arguments.subXML.xmlRoot.process.xmlAttributes.payProcessFee)>
				<cfset local.invProcessFeePercent = val(arguments.subXML.xmlRoot.process.xmlAttributes.processFeePercent)>
			<cfelse>
				<cfset local.invPayProcessFee = 0>
				<cfset local.invProcessFeePercent = 0>
			</cfif>
			
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPaidStatus">
				select statusID, statusCode
				from dbo.sub_paymentStatuses
				where statusCode = 'P'
			</cfquery>
			
			<!--- set the CF timeout to whatever it was set to previously in the code, or to 1800 (whichever is highest) --->
			<cfset local.objRequestMonitor = createObject("java", "coldfusion.runtime.RequestMonitor")>
			<cfset local.currentTimeout = local.objRequestMonitor.getRequestTimeout()>
			<cfset local.newTimeout = Max(local.currentTimeout, 1800)>
			<cfsetting requesttimeout="#local.newTimeout#">
	
			<!--- check for existing invoices --->
			<cfset local.existingInvoices = false>
			<cfif local.subscriberID neq 0>
				<cfset local.isInitialSetup = false>
				<cfset local.sidList = ''>
				<cfloop array="#XMLSearch(arguments.subXML,'//subscription/@sid')#" index="local.thisSID">
					<cfif (Len(local.thisSID.xmlValue) gt 0) AND (val(local.thisSID.xmlValue) neq 0)>
						<cfset local.sidList = ListAppend(local.sidList, val(local.thisSID.xmlValue))>
					</cfif>
				</cfloop>
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingInvoices">
					SET NOCOUNT ON;

					declare @orgID int = <cfqueryparam value="#arguments.strEventCollection.mc_siteInfo.orgID#" cfsqltype="cf_sql_integer">;
					declare @amountScheduled decimal(18,2);

					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;

					CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
					CREATE TABLE ##mcSubscriberTransactions (subscriberID int INDEX IDX_mcSubscriberTransactions_subscriberID, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
						invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
						amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
						assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

					INSERT INTO ##mcSubscribersForAcct (subscriberID)
					select li.listItem
					from dbo.fn_IntListToTable(<cfqueryparam value="#local.sidList#" cfsqltype="cf_sql_varchar">,',') as li;

					EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

					select @amountScheduled = sum(st.amountToConsider)
					from ##mcSubscriberTransactions as st;

					select st.invoiceID, convert(varchar, st.dateDue, 101) as dateDue, st.mainTransactionID, 
						s.subscriberID, s.subscriptionID, s.PCFree, st.amount as totalRemaining, @amountScheduled as totalAmountScheduled
					from dbo.sub_subscribers s
					inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
					inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = s.memberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
					where s.orgID = @orgID
					and mActive.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEventCollection.mid#">
					and ins.status = 'Pending';

					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
				</cfquery>
	
				<cfif local.qryExistingInvoices.recordCount neq 0>
					<cfset local.existingInvoices = true>
				</cfif>
				
				<cfquery dbtype="query" name="local.qryTotalBySub">
					select subscriberID, subscriptionID, sum(totalRemaining) as sumTotal
					from [local].qryExistingInvoices
					group by subscriberID, subscriptionID, PCFree					
				</cfquery>
	
				<cfif val(local.qryExistingInvoices.totalAmountScheduled) gt 0>
					<cfset local.paymentsScheduled = true>
				<cfelse>
					<cfset local.paymentsScheduled = false>
				</cfif>
			</cfif>
	
			<!--- Build a structure of dates and amounts --->
			<cfset local.dateAmts = ArrayNew(1)>
			<cfset local.pretaxTotal = 0>
			<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
				<cfif val(arguments.strEventCollection['ps_#local.thisP#_amt']) gt 0 OR arguments.strEventCollection['ps_#local.thisP#_hasiid'] gt 0>
					<cfset local.currPS = StructNew()>
					<cfset local.currPS.paymentNumber = local.thisP>
					<cfset local.currPS.date = arguments.strEventCollection['ps_#local.thisP#_date']>
					<cfset local.currPS.amt = val(arguments.strEventCollection['ps_#local.thisP#_amt'])>
					<cfset local.currPS.profiles = StructNew()>
					<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
						<cfif arguments.strEventCollection['hps_#local.thisP#_#local.thisInvProfileID#_appearOnThisInvoice']
								OR val(arguments.strEventCollection['hps_#local.thisP#_#local.thisInvProfileID#_amt']) gte 0 
								OR arguments.strEventCollection['ps_#local.thisP#_#local.thisInvProfileID#_iid'] gt 0>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"] = StructNew()>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].amt = val(arguments.strEventCollection['hps_#local.thisP#_#local.thisInvProfileID#_amt'])>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].invoiceID = arguments.strEventCollection['ps_#local.thisP#_#local.thisInvProfileID#_iid']>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].invoiceNumber = 0>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactions = ArrayNew(1)>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactionTotal = 0>
						</cfif>
					</cfloop>
					<cfset ArrayAppend(local.dateAmts, local.currPS)>
					<cfset local.pretaxTotal = local.pretaxTotal + val(local.currPS.amt)>
				</cfif>
			</cfloop>
			<cfset local.amtForCalc = local.pretaxTotal - local.ttlUpfrontAmt>
			
			<cfif local.pretaxTotal eq 0 
				AND ArrayLen(local.dateAmts) eq 0 
				AND (
					(local.subscriberID neq 0 AND local.existingInvoices eq false) 
					OR 
					XMLSearch(arguments.subXML,"count(//subscription[@alreadysub='false'])") gt 0
					)>	
				<!--- there were no existing Invoice IDs and the total was 0 
					It has to be an add with no cost.  So, add an entry for the date and cost of 0 to attach the invoice and transactions
				--->
				<cfset local.currPS = StructNew()>
				<cfset local.currPS.date = DateFormat(now(), "m/d/yyyy")>
				<cfset local.currPS.amt = 0>
				<cfset local.currPS.paymentNumber = 1>
				<cfset local.currPS.profiles = StructNew()>
				<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
					<cfset local.currPS.profiles["#local.thisInvProfileID#"] = StructNew()>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].amt = 0>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].invoiceID = 0>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].invoiceNumber = 0>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactions = ArrayNew(1)>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactionTotal = 0>
				</cfloop>
				<cfset ArrayAppend(local.dateAmts, local.currPS)>
			</cfif>
	
			<!--- numPayments is used for the actual total of the subscription, not the count of invoices --->
			<cfset local.numPayments = val(xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@rateInstallments)"))>
	
			<!--- get the recursive list of subscriptions for this subscriberID, if one is passed in 
				(if a subscriberID does not exist, it will be assumed to be new and things proceed below as normal)
				create a valuelist of subIDs from the recursive query then, while walking the loop below, 
				remove them from the list when found.
			--->
			<cfif local.subscriberID neq 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriber">
					select subscriberID, subscriptionID, typeName, subscriptionName, status, RFID, subStartDate, subEndDate, graceEndDate,
						parentSubscriberID, thePath, thePathExpanded
					from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEventCollection.mid#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEventCollection.mc_siteInfo.siteID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subscriberID#">)
				</cfquery>
			</cfif>
	
			<cfset local.removeSubList = "">
			<cfset local.readdSubList = "">
			<cfset local.topSetUID = XMLSearch(arguments.subXML,"string(//set[@id='0']/@uid)")> 
	
			<cfif local.currAction neq 'R'>
				<cfset local.topRFID = val(xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@rfid)"))>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTopProfileID">
					select profileID
					from dbo.sub_rateFrequenciesMerchantProfiles
					where rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topRFID#">
					and status <> 'D'
				</cfquery>
				<cfset local.topProfileIDList = valueList(local.qryTopProfileID.profileID)>
			</cfif>
			
			<cfloop array="#XMLSearch(arguments.subXML,'//subscription')#" index="local.thisSub">
				<cfif StructKeyExists(local.thisSub.xmlAttributes,"deleteme") AND local.thisSub.xmlAttributes.deleteme eq 1>
					<cfset local.removeSubList = listAppend(local.removeSubList, local.thisSub.xmlAttributes.sid)>
					<cfif local.thisSub.xmlAttributes.currStatus eq "R" OR local.thisSub.xmlAttributes.currStatus eq "O">
						<cfset local.thisSub.xmlAttributes.currStatus = "D">
					</cfif>
				</cfif>
				<cfif StructKeyExists(local.thisSub.xmlAttributes,"readdme") AND local.thisSub.xmlAttributes.readdme eq 1>
					<cfset local.readdSubList = listAppend(local.readdSubList, local.thisSub.xmlAttributes.sid)>
				</cfif>
			</cfloop>
			
			<cfif local.subscriberID neq 0>
				<!--- any subs removed to be removed from the table --->
				<cfloop list="#local.removeSubList#" index="local.removeSubscriberID">		
					<cfset local.objSubs.removeMemberSubscription(actorMemberID=local.actorMemberID, actorStatsSessionID=session.cfcuser.statsSessionID, memberID=local.subMemberID, subscriberID=local.removeSubscriberID, siteID=arguments.strEventCollection.mc_siteInfo.siteID, AROption='B')>
				</cfloop>
	
				<!--- find main subscription above in getting the recursive subscriptions for this subscriber (where parent subscriberID is null) to match new subscriptions to its dates --->
				<cfquery dbtype="query" name="local.qryMainSubscriber">
					select subscriberID, subscriptionID, typeName, subscriptionName, status, RFID, subStartDate, subEndDate, graceEndDate,
						parentSubscriberID, thePath, thePathExpanded
					from [local].qrySubscriber
					where status <> 'D'
					and parentSubscriberID is NULL
				</cfquery>
	
				<cfloop list="#local.readdSubList#" index="local.readdSubscriberID">		
					<cfset local.objSubs.readdMemberSubscription(actorMemberID=local.actorMemberID, memberID=local.subMemberID, subscriberID=local.readdSubscriberID, status=local.qryMainSubscriber.status, siteID=arguments.strEventCollection.mc_siteInfo.siteID)>
				</cfloop>
			<cfelse>
				<cfset local.isInitialSetup = true>
			</cfif>		

			<cfset local.thisInvoiceIDList = "">

			<!--- determine sale transaction date --->
			<cfif len(local.overrideSaleDate) gt 0>
				<cfset local.saleTransactionDate = "#local.overrideSaleDate# #timeformat(now(),'h:mm tt')#">
			<cfelse>
				<cfset local.saleTransactionDate = "#dateformat(now(),'m/d/yyyy')# #timeformat(now(),'h:mm tt')#">
			</cfif>
			
			<cfif arguments.strEventCollection.stateIDForTax gt 0 and len(arguments.strEventCollection.zipForTax)>
				<cfset local.qryAssignee = structNew()>
				<cfset local.qryAssignee.stateIDForTax = arguments.strEventCollection.stateIDForTax>
				<cfset local.qryAssignee.zipForTax = arguments.strEventCollection.zipForTax>
				<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.qryAssignee.zipForTax, billingStateID=local.qryAssignee.stateIDForTax)>
				<cfif local.strBillingZip.isvalidzip>
					<cfset local.qryAssignee.zipForTax = local.strBillingZip.billingzip>
				</cfif>
			<cfelse>
				<cfset local.qryAssignee = getStateZipForTax(orgID=arguments.strEventCollection.mc_siteInfo.orgID, memberID=local.subMemberID)>
			</cfif>

			<!--- create sql to record everything --->
			<cfsavecontent variable="local.addSubscriberSQL">
				<cfoutput>
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					<cfif arguments.bypassQueue>
						SET DEADLOCK_PRIORITY -5;
					</cfif>

					IF OBJECT_ID('tempdb..##tmpSubRegAuditLog') IS NOT NULL 
						DROP TABLE ##tmpSubRegAuditLog;
					CREATE TABLE ##tmpSubRegAuditLog (auditCode varchar(10), msg varchar(max));

					DECLARE @rc int, @invoiceID int, @subscriberID int, @loggedInMemberID int, @orgID int, @siteID int, @statsSessionID int, 
						@trashID int, @assignedToMemberID int, @subTransactionID int, @parentSubscriberID int, 
						@topParentSubscriberID int, @pendingGroupID int, @activeGroupID int, @renewGroupID int, @invstatusID int, 
						@offerDate datetime, @detail varchar(max), @GLAccountID int, @transDate datetime, @tempDate datetime, 
						@tempCompareDate datetime, @stateIDForTax int, @zipForTax varchar(25), @merchantProfileID int, @payProfileID int, @payProcessFee bit,
						@processFeePercent decimal(5,2), @couponID int, @redemptionCount int, @discountAmount decimal(18,2), @adjTransactionID int;
		
					SET @loggedInMemberID = #val(local.actorMemberID)#;
					SET @assignedToMemberID = #local.subMemberID#;
					SET @statsSessionID = #val(session.cfcuser.statsSessionID)#;
					SET @siteID = #arguments.strEventCollection.mc_siteInfo.siteID#;
					set @transDate = '#local.saleTransactionDate#';
					select @orgID = orgID from dbo.sites where siteID = @siteID;
					select @tempDate = getDate();

					set @merchantProfileID = NULLIF(#local.invMerchantProfileID#,0);
					set @payProfileID = #local.invPayProfileID#;
					set @payProcessFee = #local.invPayProcessFee#;
					set @processFeePercent = NULLIF(#local.invProcessFeePercent#,0);

					SET @parentSubscriberID = NULL;
					SET @topParentSubscriberID = null;
		
					BEGIN TRAN;

				<!--- create invoice for all dates --->
				<cfif (local.currAction neq "R") AND (local.newAsBilled eq 0) AND (local.newAsRenewed eq 0)>
					<cfif local.subscriberID eq 0>
						<cfset local.arrCount = 0>
						<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
							<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
								<cfif StructKeyExists(local.thisDateAmt.profiles, local.thisInvProfileID)>
									<cfset local.arrCount = local.arrCount + 1>
									<cfset local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber = local.arrCount>
									
									DECLARE @invoiceID_#local.arrCount# int, @invoiceNumber_#local.arrCount# varchar(19);
									
									EXEC dbo.tr_createInvoice @invoiceProfileID=#local.thisInvProfileID#, @enteredByMemberID=@loggedInMemberID, 
										@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue='#local.thisDateAmt.date#', 
										@invoiceID=@invoiceID_#local.arrCount# OUTPUT, @invoiceNumber=@invoiceNumber_#local.arrCount# OUTPUT;
									EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID_#local.arrCount#, @profileIDList='#local.topProfileIDList#';
									
									<cfif local.invPayProfileID neq 0>
										UPDATE dbo.tr_invoices
										SET payProfileID = @payProfileID,
											MPProfileID = @merchantProfileID,
											payProcessFee = @payProcessFee,
											processFeePercent = @processFeePercent
										WHERE invoiceID = @invoiceID_#local.arrCount#;

										INSERT INTO ##tmpSubRegAuditLog (auditCode, msg)
										SELECT 'INV', 'Pay Profile ' + detail + ' associated to Invoice ' + @invoiceNumber_#local.arrCount#
										FROM dbo.ams_memberPaymentProfiles
										WHERE payProfileID = @payProfileID;
										
										IF @payProcessFee = 1 BEGIN
											INSERT INTO ##tmpSubRegAuditLog (auditCode, msg)
											SELECT 'INV', 'Pay Processing Fees changed from No to Yes for Invoice ' + @invoiceNumber_#local.arrCount#;

											INSERT INTO ##tmpSubRegAuditLog (auditCode, msg)
											SELECT 'INV', 'Processing Fee Percentage changed from 0% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for Invoice ' + @invoiceNumber_#local.arrCount#;
										END
									</cfif>
									<cfset local.thisInvoiceIDList = listAppend(local.thisInvoiceIDList,local.arrCount)>
								</cfif>
							</cfloop>				
						</cfloop>

					<cfelse>	
						-- Get the existing open/pending invoices, if there are any.  Adjust old ones and create new ones as needed
						<cfset local.listUsedInvoices = ''>
						<cfset local.arrCount = 0>
						-- local.dateAmts length: #arrayLen(local.dateAmts)#
						-- local.listInvoiceProfileIDs: #local.listInvoiceProfileIDs#
						<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
							<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
								<cfif StructKeyExists(local.thisDateAmt, "profiles") AND StructKeyExists(local.thisDateAmt.profiles,"#local.thisInvProfileID#")>
									<cfparam name="arguments.strEventCollection['hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice']" default="false">
									-- local.thisDateAmt.profiles[#local.thisInvProfileID#] exists
									-- amt: #local.thisDateAmt.profiles["#local.thisInvProfileID#"].amt#
									-- hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice: #arguments.strEventCollection['hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice']#
									-- local.subscriberID: #local.subscriberID#
									-- local.existingInvoices: #local.existingInvoices#
									-- XMLSearch(arguments.subXML,"count(//subscription[@alreadysub='false'])"): #XMLSearch(arguments.subXML,"count(//subscription[@alreadysub='false'])")#
									<cfif local.thisDateAmt.profiles["#local.thisInvProfileID#"].amt gt 0 
											OR arguments.strEventCollection['hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice']
											OR (ArrayLen(local.dateAmts) eq 1 
												AND (
													(local.subscriberID neq 0 AND local.existingInvoices eq false) 
													OR
													XMLSearch(arguments.subXML,"count(//subscription[@alreadysub='false'])") gt 0
													)
												)>
										<cfset local.arrCount = local.arrCount + 1>
										<cfset local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber = local.arrCount>
										<cfset local.thisInvoiceIDList = listAppend(local.thisInvoiceIDList,local.arrCount)>
										
										DECLARE @invoiceID_#local.arrCount# int, @invoiceNumber_#local.arrCount# varchar(19);
										select @tempDate = convert(datetime,'#local.thisDateAmt.date#');
				
										<cfif local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID eq 0>
											EXEC dbo.tr_createInvoice @invoiceProfileID=#local.thisInvProfileID#, @enteredByMemberID=@loggedInMemberID, 
												@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@tempDate, 
												@invoiceID=@invoiceID_#local.arrCount# OUTPUT, @invoiceNumber=@invoiceNumber_#local.arrCount# OUTPUT;
											EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID_#local.arrCount#, @profileIDList='#local.topProfileIDList#';
			
											<cfif local.invPayProfileID neq 0>
												UPDATE dbo.tr_invoices
												set payProfileID = @payProfileID,
													MPProfileID = @merchantProfileID,
													payProcessFee = @payProcessFee,
													processFeePercent = @processFeePercent
												where invoiceID = @invoiceID_#local.arrCount#;

												INSERT INTO ##tmpSubRegAuditLog (auditCode, msg)
												SELECT 'INV', 'Pay Profile ' + detail + ' associated to Invoice ' + @invoiceNumber_#local.arrCount#
												FROM dbo.ams_memberPaymentProfiles
												WHERE payProfileID = @payProfileID;
												
												IF @payProcessFee = 1 BEGIN
													INSERT INTO ##tmpSubRegAuditLog (auditCode, msg)
													SELECT 'INV', 'Pay Processing Fees changed from No to Yes for Invoice ' + @invoiceNumber_#local.arrCount#;
		
													INSERT INTO ##tmpSubRegAuditLog (auditCode, msg)
													SELECT 'INV', 'Processing Fee Percentage changed from 0% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for Invoice ' + @invoiceNumber_#local.arrCount#;
												END
											</cfif>
										<cfelse>
											<cfset local.listUsedInvoices = listAppend(local.listUsedInvoices, local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID)>

											set @invstatusID = null;
											SELECT @invoiceID_#local.arrCount# = invoiceID, @invoiceNumber_#local.arrCount# = invoiceNumber, @tempCompareDate = dateDue, @invstatusID = statusID
											from dbo.tr_invoices
											where invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#;
											
											IF @tempCompareDate <> @tempDate BEGIN
												update dbo.tr_invoices
												set dateDue = @tempDate
												where invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#;

												INSERT INTO dbo.tr_invoiceDateHistory (invoiceID, type, updateDate, newDate, oldDate, recordedByMemberID)
												VALUES (#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#, 'D', getdate(), @tempDate, @tempCompareDate, @loggedInMemberID);

												-- add invoice transactions to limit checking table
												INSERT INTO platformQueue.dbo.queue_transactionLimitFlagging (transactionID, orgID, siteID)
												SELECT transactionID, @orgID, @siteID
												from dbo.tr_invoiceTransactions
												where orgID = @orgID
												and invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#;
											END

											IF @invstatusID <> 1 BEGIN
												update dbo.tr_invoices
												set statusID = 1
												where invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#;
											
												insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
												values (#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#, getdate(), 1, @invstatusID, @loggedInMemberID);
											END
										</cfif>
									<cfelse>
										<cfif local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID neq 0>
											<cfset local.listUsedInvoices = listAppend(local.listUsedInvoices, local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID)>
											<!--- not creating a SQL variable, if amt is 0, then we're closing this one if it exists--->
											<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryClosingInvoice">
												SELECT invoiceID, invoiceNumber, dateDue, statusID
												from dbo.tr_invoices
												where invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#
											</cfquery>
											<cfif local.qryClosingInvoice.statusID eq 2>
												update dbo.tr_invoices
												set statusID = 1
												where invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#;
												
												insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
												values (#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#, getdate(), 1, 2, @loggedInMemberID);

												<!--- amount is 0 and invoice ID exists, we're setting this to 0 and closing the invoice --->
												<cfquery dbtype="query" name="local.subPreviousAmt">
													select mainTransactionID as transactionID, subscriptionID, sum(totalRemaining) as totalRemaining
													from [local].qryExistingInvoices
													where invoiceID = #local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#
													group by invoiceID, mainTransactionID, subscriptionID
												</cfquery>
												
												<cfloop query="local.subPreviousAmt">
													<cfset local.currTransaction = StructNew()>
													<cfset local.currTransaction.action = 'Adjust'>
													<cfset local.currTransaction.amount = 0>
													<cfset local.currTransaction.adjustDifference = local.subPreviousAmt.totalRemaining * -1>
													<cfset local.currTransaction.saleTransactionID = local.subPreviousAmt.transactionID>
													<cfset local.currTransaction.invoiceCount = 0>
													<cfset local.currTransaction.subscriptionID = local.subPreviousAmt.subscriptionID>
													<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID>
			
													<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisInvProfileID#"].transactions, local.currTransaction)>			
												</cfloop>
												<cfset local.thisDateAmt.transactionTotal = 0>	
												
											</cfif>
										</cfif>
									</cfif>
								</cfif>
							</cfloop>
						</cfloop>
						
						<!--- clear unused invoices --->			
						<!--- local.listUsedInvoices --->
						<cfquery dbtype="query" name="local.qryUnusedInvoices">
							select invoiceID
							from [local].qryExistingInvoices
							where invoiceID not in (0#local.listUsedInvoices#)
						</cfquery>
						<cfif local.qryUnusedInvoices.recordCount gt 0>
							<cfloop query="local.qryUnusedInvoices">
								<!--- not creating a SQL variable, if amt is 0, then we're closing this one if it exists--->
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryClosingInvoice">
									SELECT invoiceID, invoiceNumber, dateDue, statusID, invoiceProfileID
									from dbo.tr_invoices
									where invoiceID = #local.qryUnusedInvoices.invoiceID#
								</cfquery>
								<cfif local.qryClosingInvoice.statusID eq 2>
									update dbo.tr_invoices
									set statusID = 1
									where invoiceID = #local.qryUnusedInvoices.invoiceID#;
									
									insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
									values (#local.qryUnusedInvoices.invoiceID#, getdate(), 1, 2, @loggedInMemberID);
									
									<!--- amount is 0 and invoice ID exists, we're setting this to 0 and closing the invoice --->
									<cfquery dbtype="query" name="local.subPreviousAmt">
										select mainTransactionID as transactionID, subscriptionID, sum(totalRemaining) as totalRemaining
										from [local].qryExistingInvoices
										where invoiceID = #local.qryUnusedInvoices.invoiceID#
										group by invoiceID, mainTransactionID, subscriptionID
									</cfquery>
									
									<cfloop query="local.subPreviousAmt">
										<cfif local.subPreviousAmt.totalRemaining NEQ 0>
											EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
												@statsSessionID=@statsSessionID, @amount=#local.subPreviousAmt.totalRemaining * -1#, @taxAmount=null, 
												@transactionDate=@transDate, @autoAdjustTransactionDate=1, @saleTransactionID=#val(local.subPreviousAmt.transactionID)#, 
												@invoiceID = #local.qryUnusedInvoices.invoiceID#, @byPassTax=0, @byPassAccrual=0, @xmlSchedule=null, 
												@transactionID=@trashID OUTPUT;
										</cfif>
									</cfloop>

									EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=#local.qryUnusedInvoices.invoiceID#;
								</cfif>
							</cfloop>
						</cfif>
					</cfif>
				</cfif>

				<cfset local.dateOverride = false>
				<cfif local.subscriberID eq 0>
					<cfset local.parentSubStartDate = ''>
					<cfset local.parentSubEndDate = ''>
					<cfset local.parentSubGraceEndDate = ''>
				<cfelse>
					<cfset local.retDates = getDatesToUseForExistingSubs(subXML=arguments.subXML, subStartDate=local.qryMainSubscriber.subStartDate, 
						subEndDate=local.qryMainSubscriber.subEndDate, subGraceEndDate=local.qryMainSubscriber.graceEndDate, updActiveDate=local.updActiveDate)>
					<cfif local.retDates.success>
						<cfset local.parentSubStartDate = local.retDates.subStartDate>
						<cfset local.parentSubEndDate = local.retDates.subEndDate>
						<cfset local.parentSubGraceEndDate = local.retDates.subGraceEndDate>
						<cfset local.dateOverride = local.retDates.dateOverride>
					<cfelse>
						<cfset local.parentSubStartDate = local.qryMainSubscriber.subStartDate>
						<cfset local.parentSubEndDate = local.qryMainSubscriber.subEndDate>
						<cfset local.parentSubGraceEndDate = local.qryMainSubscriber.graceEndDate>
					</cfif>
				</cfif>
				
				<!--- for each subscription, record subscriber and sale --->
				<cfif not local.invoicesOnly>
					<cfloop array="#XMLSearch(arguments.subXML,'//subscription')#" index="local.thisSub">
						<cfset local.subscriptionID = int(val(local.thisSub.xmlAttributes.id))>
						<cfset local.setID = xmlSearch(local.thisSub,'number(../@id)')>
						<cfset local.RFID = int(val(local.thisSub.xmlAttributes.rfid))>
						<cfset local.subTermFlag = local.thisSub.xmlAttributes.termflag>
						<cfset local.subPCFree = local.thisSub.xmlAttributes.pcisfree>
						<cfset local.subSubscriberID = local.thisSub.xmlAttributes.sid>
						<cfset local.subStatus = local.thisSub.xmlAttributes.currStatus>
						<cfif (local.thisSub.xmlAttributes.allowRateGLOverride eq 1) AND (local.thisSub.xmlAttributes.rateGLAccountID neq 0)>
							<cfset local.subGLAccountID = int(val(local.thisSub.xmlAttributes.rateGLAccountID))>
						<cfelse>
							<cfset local.subGLAccountID = int(val(local.thisSub.xmlAttributes.glaccountidtouse))>
						</cfif>
		
						<cfif Len(local.subStatus) eq 0>
							<cfset local.currTopStatus = xmlSearch(arguments.subXML,"string(//set[@id='0']/subscription/@currstatus)")>
							<cfif local.currTopStatus EQ 'R'>
								<cfset local.subStatus = 'R'>
							<cfelse>
								<cfset local.subStatus = 'A'>
							</cfif>
						</cfif>
						<cfset local.subName = local.thisSub.xmlAttributes.name>
		
						<!--- if no rfmpid, get the parent sub's rfmpid (this sub's grandparent) --->
						<cfif local.RFID gt 0>
							<cfset local.useRFID = local.RFID>
						<cfelse>
							<cfset local.useRFID = xmlSearch(local.thisSub,'number(../../@rfid)')>
						</cfif>
		
						<cfif local.setID eq 0 AND Len(local.parentSubStartDate) eq 0>
							<!--- check to see if date is overridden --->
							<cfif (len(local.thisSub.xmlAttributes.startDateOverride) eq 0) OR (len(local.thisSub.xmlAttributes.endDateOverride) eq 0)>
								<cfif len(local.overrideStartDate) gt 0>
									<cfset local.dateStruct = getSubscriptionDates(useRFID=local.useRFID, subTermFlag=local.subTermFlag, startDate=local.overrideStartDate, overrideCalc=true)>
								<cfelse>
									<cfset local.dateStruct = getSubscriptionDates(useRFID=local.useRFID, subTermFlag=local.subTermFlag, startDate=Now())>
								</cfif>
								<cfset local.subStartDate = local.dateStruct.subStartDate>
								<cfset local.subEndDate = local.dateStruct.subEndDate>
								<cfset local.graceEndDate = local.dateStruct.graceEndDate>
								<cfset local.parentSubStartDate = local.dateStruct.subStartDate>
								<cfset local.parentSubEndDate = local.dateStruct.subEndDate>
								<cfset local.parentSubGraceEndDate = local.dateStruct.graceEndDate>
							<cfelse>
								<cfset local.dateOverride = true>
								<cfset local.subStartDate = local.thisSub.xmlAttributes.startDateOverride>
								<cfset local.subEndDate = local.thisSub.xmlAttributes.endDateOverride>
								<cfset local.graceEndDate = local.thisSub.xmlAttributes.graceEndDateOverride>
								<cfset local.parentSubStartDate = local.subStartDate>
								<cfset local.parentSubEndDate = local.subEndDate>
								<cfset local.parentSubGraceEndDate = local.graceEndDate>
							</cfif>
						<cfelse>
							<cfset local.subStartDate = local.parentSubStartDate>
							<cfset local.subEndDate = local.parentSubEndDate>
							<cfset local.graceEndDate = local.parentSubGraceEndDate>
						</cfif>

						<cfif isDefined("local.thisSub.xmlAttributes.recogStartDate") and len(local.thisSub.xmlAttributes.recogStartDate)>
							<cfset local.recogStartDate = dateformat(local.thisSub.xmlAttributes.recogStartDate,"m/d/yyyy")>
							<cfset local.recogEndDate = dateformat(local.thisSub.xmlAttributes.recogEndDate,"m/d/yyyy") & " 23:59:59.997">
						<cfelse>
							<cfset local.recogDateStruct = getRecognitionDates(useRFID=local.useRFID, saleTransactionDate=local.saleTransactionDate, 
								subStartDate=local.subStartDate, subEndDate=local.subEndDate,rootsubTermFlag=local.subTermFlag)>
							<cfset local.recogStartDate = local.recogDateStruct.recogStartDate>
							<cfset local.recogEndDate = local.recogDateStruct.recogEndDate>
						</cfif>
						
						<cfif len(local.subStartDate) AND len(local.subEndDate)>
							<cfset local.parentSubscriberID = 0>
							
							<cfif local.setID neq 0>
								<cfset local.parentSubscriptionID = xmlSearch(local.thisSub,'number(../../@id)')>
		
								<!--- this is an edit, so the parent subscriber ID might have been previously created so check for it. --->
								<!--- if found put the result down. if not found, trust that it was created in this SQL code, so can reference it. --->
								<cfif local.subscriberID neq 0>
									<cfset local.parentSubscriberID = xmlSearch(local.thisSub,'number(../../@sid)')>
								</cfif>
								<cfif local.parentSubscriberID eq 0>							
									select @parentSubscriberID = @subscriberIDForSub#local.parentSubscriptionID#;
								<cfelse>
									select @parentSubscriberID = #local.parentSubscriberID#;
								</cfif>
							<cfelse>
								select @parentSubscriberID = NULL;
							</cfif>
						
							<cfif local.thisSub.xmlAttributes.alreadysub neq true>
							
								<cfif (local.isInitialSetup OR local.paymentsScheduled) AND (local.setID neq 0)>
									<!--- Initial creation or payments scheduled and not sold separately --->
									<cfset local.subActivationOptionCode = local.thisSub.xmlAttributes.activationoptioncode>
								<cfelse>
									<cfset local.subActivationOptionCode = local.thisSub.xmlAttributes.altactivationoptioncode>
								</cfif>
								
								<cfif (local.currAction neq "R")>
									<cfif NOT ((StructKeyExists(local.thisSub.xmlAttributes,"deleteme") AND local.thisSub.xmlAttributes.deleteme eq 1))>
										<cfif (Now() lte local.subStartDate)>
											<cfset local.subStatus = 'P'>
										</cfif>
									</cfif>
								</cfif>
								
								<cfif local.newAsRenewed eq 1>
									<cfset local.subStatusToUse = "R">
								<cfelseif (local.subStatus eq "A") OR (local.subStatus eq "P")>
									<cfset local.subStatusToUse = "O">
								<cfelse>
									<cfset local.subStatusToUse = local.subStatus>
								</cfif>

								<!--- add subscriber --->
								select @parentSubscriberID = nullif(@parentSubscriberID,0);
								EXEC dbo.sub_addSubscriber @orgID=@orgID, @memberID=@assignedToMemberID, @subscriptionID=#local.subscriptionID#,
									@parentSubscriberID=@parentSubscriberID, @RFID=#local.RFID#, @GLAccountID=#local.subGLAccountID#,
									@status='#local.subStatusToUse#', 
									@subStartDate='#dateFormat(local.subStartDate,"yyyy-mm-dd")# #timeFormat(local.subStartDate, "HH:mm:ss")#', 
									@subEndDate='#dateFormat(local.subEndDate,"yyyy-mm-dd")# #timeFormat(local.subEndDate, "HH:mm:ss")#', 
									@graceEndDate=<cfif len(local.graceEndDate) gt 0>'#dateFormat(local.graceEndDate,"yyyy-mm-dd")# #timeFormat(local.graceEndDate, "HH:mm:ss")#'<cfelse>NULL</cfif>,
									@recogStartDate='#local.recogStartDate#', @recogEndDate='#local.recogEndDate#', 
									@pcfree=<cfif local.subPCFree eq "false">0<cfelse>1</cfif>,
									@activationOptionCode='#local.subActivationOptionCode#',
									@recordedByMemberID=@loggedInMemberID, @bypassQueue=1, @subscriberID=@subscriberID OUTPUT;
								
								IF @parentSubscriberID is NULL
									SELECT @topParentSubscriberID = @subscriberID;
								
								<cfif local.skipEmailTemplateNotifications is 1>
									EXEC dbo.sub_suppressSubscriberAutomatedEmail @subscriberID=@subscriberID, @enteredByMemberID=@loggedInMemberID, @limitToEmailTemplateID=NULL;
								</cfif>

								<cfif (local.newAsBilled eq 0) AND (local.newAsRenewed eq 0)>
									<cfif (local.subStatus eq "A")>
										EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID, @newStatusCode='P', @siteID=@siteID, 
											@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@rc OUTPUT;
										IF @rc = 1
											EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID, @newStatusCode='A', @siteID=@siteID,
												@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@rc OUTPUT;
									<cfelseif (local.subStatus eq "P")>
										EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='P', @siteID=@siteID,
											@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@rc OUTPUT;
									</cfif>
								</cfif>

							<cfelse>
								<cfif local.thisSub.xmlAttributes.currStatus eq "R" OR local.thisSub.xmlAttributes.currStatus eq "O">
									update dbo.sub_subscribers
									set RFID = #local.RFID#,
										PCFree = <cfif local.subPCFree eq "false">0<cfelse>1</cfif>,
										<cfif local.dateOverride>
											subStartDate = '#local.subStartDate#',
											subEndDate = '#DateFormat(local.subEndDate,"m/d/yyyy")# 23:59:59.997',
											graceEndDate = <cfif len(local.graceEndDate) gt 0>'#DateFormat(local.graceEndDate,"m/d/yyyy")# 23:59:59.997'<cfelse>NULL</cfif>,
											expectedSubendDate = '#DateFormat(local.subEndDate,"m/d/yyyy")# 23:59:59.997',
											expectedGraceEndDate = <cfif len(local.graceEndDate) gt 0>'#DateFormat(local.graceEndDate,"m/d/yyyy")# 23:59:59.997'<cfelse>NULL</cfif>,
											recogStartDate = '#local.recogStartDate#',
											recogEndDate = '#DateFormat(local.recogEndDate,"m/d/yyyy")# 23:59:59.997',
										</cfif>
										GLAccountID = #local.subGLAccountID#
									where subscriberID = #local.subSubscriberID#;
								</cfif>
							
								select @subscriberID = #local.subSubscriberID#;
							</cfif>
	
							<cfset local.saveRateToUse = getRateToUse(useRates=local.thisSub.xmlAttributes.useRates, rateAmt=local.thisSub.xmlAttributes.rateAmt, 
								rateInstallments=local.thisSub.xmlAttributes.rateInstallments, numPaymentsToUse=local.numPayments, 
								pcPctOff=local.thisSub.xmlAttributes.pcpctoff, pcRateAmt=local.thisSub.xmlAttributes.pcrateamt)>
							update dbo.sub_subscribers
							set lastPrice = convert(decimal(18,2), '#local.saveRateToUse.rateTotal#')
								<cfif local.thisSub.xmlAttributes.useRates eq 0 
									AND (
										local.thisSub.xmlAttributes.pricechanged EQ 1 OR
										(local.thisSub.xmlAttributes.keyExists("origpricechanged") AND local.thisSub.xmlAttributes.origpricechanged EQ 1)
									)>
									, modifiedRate = convert(decimal(18,2), '#local.thisSub.xmlAttributes.pcrateamt#')
								<cfelse>
									, modifiedRate = null
								</cfif>
							where subscriberID = @subscriberID;
												
							declare @subscriberIDForSub#local.subscriptionID# int;
							select @subscriberIDForSub#local.subscriptionID# = @subscriberID;
						</cfif>
		
						<cfif (local.currAction neq "R") 
								AND (local.thisSub.xmlAttributes.currStatus eq "R" OR local.thisSub.xmlAttributes.currStatus eq "O")
								AND NOT ((StructKeyExists(local.thisSub.xmlAttributes,"deleteme") AND local.thisSub.xmlAttributes.deleteme eq 1))>
							<cfif local.thisSub.xmlAttributes.sid neq "0">
								select @subscriberID = #local.thisSub.xmlAttributes.sid#;
							</cfif>

							<!--- change the status based on start time.  If start time is after today, status is P, otherwise, it's A --->
							<cfif Now() lte local.subStartDate>
								<cfset local.subUpdateStatus = 'P'>
							<cfelse>
								<cfset local.subUpdateStatus = 'A'>
							</cfif>							

							<cfif local.skipEmailTemplateNotifications is 1>
								EXEC dbo.sub_suppressSubscriberAutomatedEmail @subscriberID=@subscriberID, @enteredByMemberID=@loggedInMemberID, @limitToEmailTemplateID=NULL;
							</cfif>

							EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='#local.subUpdateStatus#', @siteID=@siteID, 
								@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@trashID OUTPUT;
						<cfelseif local.thisSub.xmlAttributes.currStatus eq "P" and local.skipEmailTemplateNotifications>
							EXEC dbo.sub_suppressSubscriberAutomatedEmail @subscriberID=@subscriberID, @enteredByMemberID=@loggedInMemberID, @limitToEmailTemplateID=NULL;
						</cfif>
	
						<cfset local.dateOverrideCode = getDateOverrideCode(local.dateOverride, local.parentSubStartDate, local.updActiveDate)>
						#local.dateOverrideCode#
					</cfloop>
					EXEC dbo.sub_fixSubscriberTreeOrder @rootSubscriberID=@topParentSubscriberID;

				<cfelse>
					<cfloop array="#XMLSearch(arguments.subXML,'//subscription')#" index="local.thisSub">
						<cfset local.subscriptionID = int(val(local.thisSub.xmlAttributes.id))>
						<cfset local.subSubscriberID = local.thisSub.xmlAttributes.sid>
						declare @subscriberIDForSub#local.subscriptionID# int;
						select @subscriberIDForSub#local.subscriptionID# = #local.subSubscriberID#;
					</cfloop>
				</cfif>

				<cfif (local.currAction neq "R") AND (local.newAsBilled eq 0) AND (local.newAsRenewed eq 0)>
					<cfif local.subscriberID neq 0>
						<cfset local.dateAmts = getSubscriptionTransactions(subXML=arguments.subXML, numPayments=local.numPayments, dateAmts=local.dateAmts, subEndDate=local.parentSubEndDate, qryExistingInvoices=local.qryExistingInvoices)>
					<cfelse>
						<cfset local.dateAmts = getSubscriptionTransactions(subXML=arguments.subXML, numPayments=local.numPayments, dateAmts=local.dateAmts, subEndDate=local.parentSubEndDate)>
					</cfif>

					<!--- prepare invoices --->
					<cfset local.profilesAdjusts = StructNew()>
					<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
						<cfset local.profilesAdjusts["#local.thisInvProfileID#"] = StructNew()>
						<cfset local.profilesAdjusts["#local.thisInvProfileID#"].invoiceAdjustArray = ArrayNew(1)>
					</cfloop>
					
					<cfset local.arrIndex = 0>
					<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
						<cfset local.arrIndex = local.arrIndex + 1>
						<cfset local.arrProfileKeys = StructKeyArray(local.thisDateAmt.profiles)>
						<cfloop array="#local.arrProfileKeys#" index="local.thisProfileKey">
							<!--- check that payments equal the invoice amount --->
							<cfif (Len(local.thisDateAmt.profiles["#local.thisProfileKey#"].amt) gt 0) AND (local.thisDateAmt.profiles["#local.thisProfileKey#"].amt neq 0)>
								<cfif decimalFormat(local.thisDateAmt.profiles["#local.thisProfileKey#"].transactionTotal - local.thisDateAmt.profiles["#local.thisProfileKey#"].amt) neq 0>
									<!--- need to adjust numbers before committing transactions --->
									<cfset local.invoiceAdjustStruct = StructNew()>
									<cfset local.invoiceAdjustStruct.difference = decimalFormat(local.thisDateAmt.profiles["#local.thisProfileKey#"].amt - local.thisDateAmt.profiles["#local.thisProfileKey#"].transactionTotal)>
									<cfset local.invoiceAdjustStruct.arrIndex = local.arrIndex>
									<cfset ArrayAppend(local.profilesAdjusts["#local.thisProfileKey#"].invoiceAdjustArray, local.invoiceAdjustStruct)>
								</cfif> <!--- decimalFormat(local.thisDateAmt.profiles["#local.thisProfileKey#"].transactionTotal --->
							</cfif> <!--- end if (Len(local.thisDateAmt.profiles["#local.thisProfileKey#"].amt) gt 0)  --->
						</cfloop> <!--- end local.arrProfileKeys loop --->
					</cfloop> <!--- end local.dateAmts loop --->
					
					<cfset local.profileAdjustKeys = StructKeyArray(local.profilesAdjusts)>
					<cfloop array="#local.profileAdjustKeys#" index="local.thisProfileKey">
						<cfset local.invoiceAdjustArray = local.profilesAdjusts["#local.thisProfileKey#"].invoiceAdjustArray>
						<cfif ArrayLen(local.invoiceAdjustArray) gt 1> <!--- have to have at least 2 to adjust or leave it alone --->
							<!--- loop through all the subs in the first one, currently, the number of transactions should line up --->
							<cfset local.thisSubIDWillWork = 0>
							<cfloop array="#local.dateAmts[local.invoiceAdjustArray[1].arrIndex].profiles[local.thisProfileKey].transactions#" index="local.thisSubTransactionInfo">
								<cfset local.loopCurrSubID = local.thisSubTransactionInfo.subscriptionID>
							
								<cfset local.currArrLoop = 0>
								<cfloop array="#local.invoiceAdjustArray#" index="local.thisArrIndex">
									<cfset local.currArrLoop = local.currArrLoop + 1>
									<!--- find the subscription, check the test, break if no good --->
									<cfset local.loopAmtWorks = false>
									<cfloop array="#local.dateAmts[local.thisArrIndex.arrIndex].profiles[local.thisProfileKey].transactions#" index="local.thisLoopTransactionInfo">
										<cfif local.thisLoopTransactionInfo.subscriptionID eq local.loopCurrSubID>
											<cfset local.loopSubFound = true>
											<cfif local.thisLoopTransactionInfo.action eq "Add">
												<cfif decimalFormat(local.thisLoopTransactionInfo.amount + local.thisArrIndex.difference) gte 0>
													<cfset local.loopAmtWorks = true>
													<cfbreak />
												</cfif> <!--- end if decimalFormat(local.thisLoopTransactionInfo.amount + local.thisArrIndex.difference) gte 0 --->
											<cfelseif local.thisLoopTransactionInfo.action eq "Adjust">
												<cfif (local.thisLoopTransactionInfo.adjustDifference + local.thisArrIndex.difference) gte 0>
													<cfset local.loopAmtWorks = true>
													<cfbreak /> 
												</cfif> <!--- end if (local.thisLoopTransactionInfo.adjustDifference + local.thisArrIndex.difference) gte 0 --->
											<cfelse>
												<!--- unknown action --->
												<cfbreak />
											</cfif> <!--- end if/elseif local.thisLoopTransactionInfo.action eq "Add" --->
										</cfif> <!--- end if local.thisLoopTransactionInfo.subscriptionID eq local.loopCurrSubID --->
									</cfloop> <!--- local.dateAmts[local.thisArrIndex.arrIndex].profiles[local.thisProfileKey].transactions --->
	
									<cfif local.loopAmtWorks neq true>
										<cfbreak /> <!--- go to the next sub --->
									<cfelseif local.currArrLoop eq ArrayLen(local.invoiceAdjustArray)> <!--- last one and if here it all worked --->
										<cfset local.thisSubIDWillWork = local.loopCurrSubID>
										<cfbreak />
									</cfif> <!--- end if/elseif local.loopAmtWorks neq true --->
	
								</cfloop> <!--- end local.invoiceAdjustArray loop --->
								
								<cfif local.thisSubIDWillWork neq 0> 
									<!--- do the work and get out --->
									<cfloop array="#local.invoiceAdjustArray#" index="local.thisAdjustIndex">
										<cfloop array="#local.dateAmts[local.thisAdjustIndex.arrIndex].profiles[local.thisProfileKey].transactions#" index="local.thisLoopAdjustTransactionInfo">
											<cfif local.thisLoopAdjustTransactionInfo.subscriptionID eq local.loopCurrSubID>
												<cfif local.thisLoopAdjustTransactionInfo.action eq "Add">
													<cfset local.thisLoopAdjustTransactionInfo.amount = decimalFormat(local.thisLoopAdjustTransactionInfo.amount + local.thisAdjustIndex.difference)>
												<cfelseif local.thisLoopAdjustTransactionInfo.action eq "Adjust">
													<cfset local.thisLoopAdjustTransactionInfo.adjustDifference = local.thisLoopAdjustTransactionInfo.adjustDifference + local.thisAdjustIndex.difference>
												</cfif> <!--- end if/elseif local.thisLoopAdjustTransactionInfo.action eq "Add" --->
											</cfif> <!--- end if local.thisLoopAdjustTransactionInfo.subscriptionID eq local.loopCurrSubID --->
										</cfloop> <!--- end local.dateAmts[local.thisAdjustIndex.arrIndex].profiles[local.thisProfileKey].transactions loop --->
									</cfloop> <!--- end local.invoiceAdjustArray loop --->
									<cfbreak /> <!--- done, we've got our sub adjust --->
								</cfif> <!--- end if local.thisSubIDWillWork neq 0 --->
							</cfloop>	<!--- end local.dateAmts[local.invoiceAdjustArray[1].arrIndex].profiles[local.thisProfileKey].transactions loop --->				
						
						</cfif> <!--- end if ArrayLen(local.invoiceAdjustArray) gt 1 ---> 
					</cfloop> <!--- end local.profileAdjustKeys loop --->

					<!--- create recognition schedules --->
					<cfset local.recogSchedCode = getRecognitionScheduleCode(dateAmts=local.dateAmts)>
					#local.recogSchedCode#

					<!--- enter transactions. trust amounts after this point --->
					<cfset local.transInvoiceCode = getTransactionsAndInvoiceCode(subXML=arguments.subXML, dateAmts=local.dateAmts, subMemberID=local.subMemberID, 
						listInvoiceProfileIDs=local.listInvoiceProfileIDs, thisInvoiceIDList=local.thisInvoiceIDList, 
						strEventCollection=arguments.strEventCollection, transDate=local.saleTransactionDate,
						stateIDforTax=local.qryAssignee.stateIDForTax, zipForTax=local.qryAssignee.zipForTax)>
					#local.transInvoiceCode#
				</cfif>

						-- audit log
						IF EXISTS (SELECT 1 FROM ##tmpSubRegAuditLog) BEGIN
							INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
							SELECT '{ "c":"auditLog", "d": {
								"AUDITCODE":"' + auditCode + '",
								"ORGID":' + cast(@orgID as varchar(10)) + ',
								"SITEID":' + cast(@siteID as varchar(10)) + ',
								"ACTORMEMBERID":' + cast(@loggedInMemberID as varchar(20)) + ',
								"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
								"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
							FROM ##tmpSubRegAuditLog;
						END

						-- clear subXML
						DELETE FROM platformStatsMC.dbo.sub_adminSubLog
						WHERE actorMemberID = @loggedInMemberID
						AND memberID = @assignedToMemberID;

					COMMIT TRAN;

					-- check activations for all subs for the member
					EXEC dbo.sub_checkActivationsByMember @orgID=@orgID, @memberid=@assignedToMemberID, @subscriberID=null, @bypassQueue=1;
				
					-- reprocess any applicable conditions. ConditionsAndGroups here not ConditionsAndGroupsChanged because of the manual subscription groups
					BEGIN TRY
						IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
							DROP TABLE ##tblMCQRun;
						CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

						INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
						SELECT @orgID, #local.subMemberID#, conditionID
						FROM dbo.ams_virtualGroupConditions
						where orgID = @orgID
						and fieldCode = 'sub_entry';

						EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroups';

						IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
							DROP TABLE ##tblMCQRun;
					END TRY
					BEGIN CATCH
						EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
					END CATCH

					SELECT 1 as success, @topParentSubscriberID as topParentSubscriberID;

					IF OBJECT_ID('tempdb..##tmpSubRegAuditLog') IS NOT NULL 
						DROP TABLE ##tmpSubRegAuditLog;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SELECT 0 AS success;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
				</cfoutput>
			</cfsavecontent>

			<!--- run generated sql --->
			<cftry>
				<cfquery name="local.qryAddSubscribers" datasource="#application.dsn.membercentral.dsn#" timeout="#local.newTimeout#">
					#preserveSingleQuotes(local.addSubscriberSQL)#
				</cfquery>
				<cfif local.keyExists("qryAddSubscribers") AND local.qryAddSubscribers.success is not 1>
					<cfthrow message="local.qryAddSubscribers was not successful">
				</cfif>
			<cfcatch type="any">
				<cfset application.objError.extendRequestTimeout()>	
				<cfset local.addSubscriberSQL = replace(local.addSubscriberSQL,chr(10),"<br/>","ALL")>
				<cfset local.arguments = duplicate(arguments)>
				<cfset local.rethrowErr = true>
				<cfset local.customMessage = "model.admin.subscriptions.subscriptionReg.doConfirm Run SQL">
				<cfif arguments.bypassQueue>
					<cfset local.errmessage = "#cfcatch.detail#; #cfcatch.message#">
					<cfset local.qryAddSubscribers = { 
						"success": 0, 
						"topParentSubscriberID": 0, 
						"errcode": findNoCase("Errno 1205:",local.errmessage) AND findNoCase("deadlock",local.errmessage) ? "SQLDEADLOCK" : "SUBSQLRUNFAIL",
						"errmessage": local.errmessage
					}>
					<cfset local.customMessage = "#local.customMessage# [Called from Queued Process]">
					<cfset local.rethrowErr = local.qryAddSubscribers.errcode EQ 'SQLDEADLOCK' ? false : true>
				</cfif>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage=local.customMessage)>
				<cfif local.rethrowErr>
					<cfrethrow>
				</cfif>
			</cfcatch>
			</cftry>

		<cfcatch type="any">
			<cfset errorStruct.localscope = local>
			<cfset errorStruct.event = duplicate(arguments.strEventCollection)>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=errorStruct, customMessage="model.admin.subscriptions.subscriptionReg.doConfirm Wrapper")>
			<cfrethrow>
		</cfcatch>
		</cftry>

		<!--- if a success --->
		<!--- Don't delete the xml, the doSubscribe is still using it... --->
		<cfset local.retStruct = StructNew()>
		<cfset local.retStruct.sqlTemp = local.addSubscriberSQL>
		<cfset local.retStruct.success = local.qryAddSubscribers.success>
		<cfset local.retStruct.errorSection = 0>
		<cfset local.retStruct.topParentSubscriberID = local.qryAddSubscribers.topParentSubscriberID>
		<cfif NOT local.retStruct.success AND local.qryAddSubscribers.keyExists("errcode")>
			<cfset local.retStruct.errcode = local.qryAddSubscribers.errcode>
			<cfset local.retStruct.errMessage = local.qryAddSubscribers.errMessage>
		</cfif>

		<cfset local.rootSubscriptionID = xmlSearch(arguments.subXML,"string(//set[@id='0']/subscription/@id)")>
		<cfset local.couponKey = "subCoupon_#local.subMemberID#_#local.rootSubscriptionID#">
		<cfif application.mcCacheManager.sessionValueExists(local.couponKey)>
			<cfset application.mcCacheManager.sessionDeleteValue(local.couponKey)>
		</cfif>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getRecognitionScheduleCode" access="private" output="false" returntype="string">
		<cfargument name="dateAmts" type="array" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.retString">
			<cfoutput>
			-- create recognition schedules
			declare @tblRecognitions TABLE (autoID int IDENTITY(1,1), dateamtInvNum int, dateamtTransNumber int, dueDate datetime, amount decimal(18,2), subscriptionID int, subscriptionTransOrder int);
			declare @tblSubscriberID TABLE (subscriptionID int, subscriberID int);
			declare @minSubIDDeferred int, @defStartDate datetime, @defEndDate datetime, @minSubTransOrder int, @recogThisMonth datetime, 
				@subAmtSum decimal(18,2), @spreadSum decimal(18,2), @subScheduleCount int, @spreadFirstAutoID int, @spreadDiff decimal(18,2),
				@transAmtToRecog decimal(18,2), @recogAmt decimal(18,2), @defSchAmt decimal(18,2), @recogAutoID int, @XMLSchedule xml;
			declare @tblDefSchedule TABLE (autoID int IDENTITY(1,1), subscriptionID int, dt datetime, amt decimal(18,2));
			declare @tblFinalDefSchedule TABLE (subscriptionID int, subscriptionTransOrder int, dt datetime, amt decimal(18,2));

			<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
				<cfloop collection="#local.thisDateAmt.profiles#" item="local.thisProfileKey">
					<cfset local.thisArrTransactions = local.thisDateAmt.profiles[local.thisProfileKey].transactions>
					<cfloop from="1" to="#ArrayLen(local.thisArrTransactions)#" index="local.thisCurrTransactionNum">
						<cfif structKeyExists(local.thisArrTransactions[local.thisCurrTransactionNum],"deferredGLAccountID") 
							and local.thisArrTransactions[local.thisCurrTransactionNum].deferredGLAccountID gt 0
							and (
								local.thisArrTransactions[local.thisCurrTransactionNum].action eq "Add"
								or 
								(local.thisArrTransactions[local.thisCurrTransactionNum].action eq "Adjust" AND local.thisArrTransactions[local.thisCurrTransactionNum].adjustDifference gt 0)
								)>
							insert into @tblRecognitions (dateamtInvNum, dateamtTransNumber, dueDate, amount, subscriptionID) 
							values (#local.thisDateAmt.profiles[local.thisProfileKey].invoiceNumber#, #local.thisCurrTransactionNum#, '#local.thisDateAmt.date#', <cfif local.thisArrTransactions[local.thisCurrTransactionNum].action eq "Adjust">#local.thisArrTransactions[local.thisCurrTransactionNum].adjustDifference#<cfelse>#local.thisArrTransactions[local.thisCurrTransactionNum].amount#</cfif>, #local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#);

							IF NOT EXISTS (select subscriptionID from @tblSubscriberID where subscriptionID = #local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#)
								insert into @tblSubscriberID (subscriptionID, subscriberID)
								VALUES (#local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#, @subscriberIDForSub#local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#);
						</cfif>
					</cfloop>
				</cfloop>
			</cfloop>		

			-- update order we need to use below (invduedate asc)
			update tbl
			set tbl.subscriptionTransOrder = tmp.row
			from @tblRecognitions as tbl
			inner join (
				select autoID, ROW_NUMBER() OVER(PARTITION BY subscriptionID ORDER BY dueDate, dateAmtInvNum, dateamtTransNumber) as row
				from @tblRecognitions
			) as tmp on tmp.autoID = tbl.autoID;

			-- loop over each subscription in deferred
			select @minSubIDDeferred = min(subscriptionID) from @tblRecognitions;
			while @minSubIDDeferred is not null BEGIN
				select @defStartDate = s.recogStartDate, @defEndDate = s.recogEndDate
					from dbo.sub_subscribers as s
					inner join @tblSubscriberID as tbl on tbl.subscriberID = s.subscriberID
					where tbl.subscriptionID = @minSubIDDeferred;

				-- create recog schedule for sub
				set @recogThisMonth = DATEADD(dd,DATEDIFF(dd,0,@defStartDate),0);
				while @recogThisMonth <= @defEndDate BEGIN
					insert into @tblDefSchedule (subscriptionID, dt, amt) values (@minSubIDDeferred, @recogThisMonth, 0);
					select @recogThisMonth = DATEADD(mm,1,@recogThisMonth);
				END

				-- spread amount of sub over dates evenly and put remainder on 1st schedule item
				select @subAmtSum = sum(amount) from @tblRecognitions where subscriptionID = @minSubIDDeferred;
				select @subScheduleCount = count(*) from @tblDefSchedule where subscriptionID = @minSubIDDeferred;
				update @tblDefSchedule set amt = cast(@subAmtSum/@subScheduleCount as decimal(18,2)) where subscriptionID = @minSubIDDeferred;
				select @spreadSum = sum(amt), @spreadFirstAutoID = min(autoid) from @tblDefSchedule where subscriptionID = @minSubIDDeferred;
				set @spreadDiff = @subAmtSum - @spreadSum;
				if @spreadDiff <> 0
					update @tblDefSchedule set amt = amt + @spreadDiff where autoID = @spreadFirstAutoID;

				-- loop over transactions in order to determine the schedule needed for each transaction
				set @minSubTransOrder = null;
				select @minSubTransOrder = min(subscriptionTransOrder) from @tblRecognitions where subscriptionID = @minSubIDDeferred;
				while @minSubTransOrder is not null BEGIN
					select @transAmtToRecog = amount from @tblRecognitions where subscriptionID = @minSubIDDeferred and subscriptionTransOrder = @minSubTransOrder;

					while @transAmtToRecog > 0 BEGIN
						select top 1 @recogAutoID = autoID, @recogThisMonth = dt, @recogAmt = amt
							from @tblDefSchedule 
							where subscriptionID = @minSubIDDeferred
							and amt > 0
							order by autoID;

						if @recogAmt <= @transAmtToRecog
							set @defSchAmt = @recogAmt;
						else 									
							set @defSchAmt = @transAmtToRecog;

						insert into @tblFinalDefSchedule (subscriptionID, subscriptionTransOrder, dt, amt)
						values (@minSubIDDeferred, @minSubTransOrder, @recogThisMonth, @defSchAmt);

						update @tblDefSchedule 
						set amt = amt - @defSchAmt 
						where subscriptionID = @minSubIDDeferred
						and autoID = @recogAutoID;

						set @transAmtToRecog = @transAmtToRecog - @defSchAmt;
					END

					select @minSubTransOrder = min(subscriptionTransOrder) from @tblRecognitions where subscriptionID = @minSubIDDeferred and subscriptionTransOrder > @minSubTransOrder;
				END

				select @minSubIDDeferred = min(subscriptionID) from @tblRecognitions where subscriptionID > @minSubIDDeferred;
			END
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.retString>
	</cffunction>

	<cffunction name="getTransactionsAndInvoiceCode" access="private" output="false" returntype="string">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="dateAmts" type="array" required="true">
		<cfargument name="subMemberID" type="numeric" required="true">
		<cfargument name="listInvoiceProfileIDs" type="string" required="true">
		<cfargument name="thisInvoiceIDList" type="string" required="true">
		<cfargument name="strEventCollection" type="struct" required="true">
		<cfargument name="transdate" type="date" required="true">
		<cfargument name="stateIDforTax" type="numeric" required="true">
		<cfargument name="zipForTax" type="string" required="true">

		<cfset var local = structNew()>

		<cfset local.strSubDiscounts = structNew('ordered')>
		<cfset local.rootStatus = xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@currstatus)")>
		<cfif (NOT Len(local.rootStatus) OR listFindNoCase("R,O",local.rootStatus))>
			<cfset local.rootSubscriptionID = xmlSearch(arguments.subXML,"string(//set[@id='0']/subscription/@id)")>
			<cfset local.couponKey = "subCoupon_#arguments.subMemberID#_#local.rootSubscriptionID#">
			<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname=local.couponKey, defaultValue={})>
			
			<!--- coupon applied --->
			<cfif structCount(local.strCoupon) AND val(local.strCoupon.couponID)>
				<cfset local.totalRedemptionCount = 0>

				<cfloop collection="#local.strCoupon.strsubprice#" item="local.subID">
					<cfset local.thisSub = duplicate(local.strCoupon.strsubprice[local.subID])>
					<cfset local.discount = val(local.thisSub.discount)>

					<!--- no discount --->
					<cfif local.discount EQ 0>
						<cfcontinue>
					</cfif>

					<cfset local.tmpStr = { "discount":local.discount, "discountRemaining":local.discount, "discountPerInstallment":local.thisSub.discountperinstallment, "strDates":structNew('ordered') }>
					<cfset local.thisSubTotalSaleAmt = 0>

					<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
						<cfset local.dateProfileKeys = StructKeyArray(local.thisDateAmt.profiles)>
						<cfloop array="#local.dateProfileKeys#" index="local.thisProfileKey">
							<cfset local.thisArrTransactions = duplicate(local.thisDateAmt.profiles[local.thisProfileKey].transactions)>
							<cfloop from="1" to="#ArrayLen(local.thisArrTransactions)#" index="local.thisCurrTransactionNum">
								<cfset local.thisCurrTransaction = local.thisArrTransactions[local.thisCurrTransactionNum]>
								<cfif local.thisCurrTransaction.subscriptionID EQ local.subID AND local.thisCurrTransaction.action eq "Add" AND local.thisCurrTransaction.amount GT 0>
									<cfset local.thisSubTotalSaleAmt = local.thisSubTotalSaleAmt + local.thisCurrTransaction.amount>
									<cfset local.discountStr = { "amt":local.thisCurrTransaction.amount, "discount":0, "redemptionCount":0 }>
									<cfif local.thisDateAmt.date EQ arguments.dateAmts[1].date
										AND (
											local.strCoupon.applyto EQ 'sub' 
											OR
											(local.strCoupon.applyto EQ 'subtree' AND local.totalRedemptionCount EQ 0)
										)>
										<cfset local.discountStr.redemptionCount = 1>
										<cfset local.totalRedemptionCount++>
									</cfif>
									<cfset structInsert(local.tmpStr.strDates, DateFormat(local.thisDateAmt.date,'m_d_yyyy'), local.discountStr)>
								</cfif>
							</cfloop>
						</cfloop>
					</cfloop>

					<!--- no sales --->
					<cfif local.thisSubTotalSaleAmt EQ 0>
						<cfcontinue>
					</cfif>

					<cfif local.tmpStr.discount GT local.thisSubTotalSaleAmt>
						<cfset local.tmpStr.discount = local.thisSubTotalSaleAmt>
						<cfset local.tmpStr.discountRemaining = local.thisSubTotalSaleAmt>
					</cfif>

					<cfloop condition="local.tmpStr.discountRemaining GT 0">
						<cfloop collection="#local.tmpStr.strDates#" item="local.thisDate">
							<cfset local.thisDateSub = duplicate(local.tmpStr.strDates[local.thisDate])>

							<!--- max discount applied to this sale --->
							<cfif local.thisDateSub.amt EQ local.thisDateSub.discount>
								<cfcontinue>
							</cfif>

							<cfset local.thisSubSaleDiscount = MIN((local.thisDateSub.amt - local.thisDateSub.discount),MIN(local.tmpStr.discountPerInstallment,local.tmpStr.discountRemaining))>
							<cfset local.tmpStr.strDates[local.thisDate].discount = NumberFormat(precisionEvaluate(local.thisDateSub.discount + local.thisSubSaleDiscount),"0.00")>
							<cfset local.tmpStr.discountRemaining = NumberFormat(precisionEvaluate(local.tmpStr.discountRemaining - local.thisSubSaleDiscount),"0.00")>

							<!--- discount fully applied --->
							<cfif local.tmpStr.discountRemaining EQ 0>
								<cfbreak>
							</cfif>
						</cfloop>
					</cfloop>

					<cfset structInsert(local.strSubDiscounts, local.subID, local.tmpStr)>
				</cfloop>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.retString">
			<cfoutput>
			<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
				<cfset local.dateProfileKeys = StructKeyArray(local.thisDateAmt.profiles)>
				<cfloop array="#local.dateProfileKeys#" index="local.thisProfileKey">
					<cfset local.thisArrTransactions = local.thisDateAmt.profiles[local.thisProfileKey].transactions>
					<cfloop from="1" to="#ArrayLen(local.thisArrTransactions)#" index="local.thisCurrTransactionNum">
						<cfset local.thisCurrTransaction = local.thisArrTransactions[local.thisCurrTransactionNum]>
						<cfif local.thisCurrTransaction.action eq "Add">
							<cfset local.strTaxIndiv = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.thisCurrTransaction.GLAccountID, 
								saleAmount=local.thisCurrTransaction.amount, transactionDate=arguments.transdate, stateIDForTax=val(arguments.stateIDforTax),
								zipForTax=arguments.zipForTax)>
							set @XMLSchedule = null;
							SELECT @XMLSchedule = (
								select row.amt, convert(varchar(10),row.dt,101) as dt
								from @tblFinalDefSchedule as row
								inner join @tblRecognitions as r on r.subscriptionID = row.subscriptionID and r.subscriptionTransOrder = row.subscriptionTransOrder
								where r.subscriptionID = #local.thisCurrTransaction.subscriptionID#
								and r.dateamtInvNum = #local.thisDateAmt.profiles[local.thisProfileKey].invoicenumber#
								and r.dateAmtTransNumber = #local.thisCurrTransactionNum#
								for XML AUTO, ROOT('rows'), TYPE
							);

							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID,
								@statsSessionID=@statsSessionID, @status='Active', @detail='#replace(left(local.thisCurrTransaction.detail,500),"'","''","ALL")#', @amount=#local.thisCurrTransaction.amount#,
								@transactionDate=@transDate, @creditGLAccountID=#local.thisCurrTransaction.GLAccountID#, 
								@invoiceID=<cfif local.thisCurrTransaction.invoiceCount neq 0>@invoiceID_#local.thisCurrTransaction.invoiceCount#<cfelse>#local.thisCurrTransaction.invoiceID#</cfif>,
								@parentTransactionID=null, 
								@stateIDForTax=<cfif val(arguments.stateIDforTax)>#arguments.stateIDforTax#<cfelse>null</cfif>, 
								@zipForTax=<cfif len(arguments.zipForTax)>'#arguments.zipForTax#'<cfelse>null</cfif>, 
								@taxAmount=#val(local.strTaxIndiv.totalTaxAmt)#, @byPassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, 
								@xmlSchedule=@XMLSchedule, @transactionID=@subTransactionID OUTPUT;

							EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Admin', @transactionID=@subTransactionID, 
								@itemType='Dues', @itemID=@subscriberIDForSub#local.thisCurrTransaction.subscriptionID#, @subItemID=null;

							<!--- Coupon applied --->
							<cfif local.strSubDiscounts.keyExists(local.thisCurrTransaction.subscriptionID) 
								AND local.strSubDiscounts[local.thisCurrTransaction.subscriptionID].strDates.keyExists(DateFormat(local.thisDateAmt.date,'m_d_yyyy'))>
								<cfset local.thisSubDiscount = duplicate(local.strSubDiscounts[local.thisCurrTransaction.subscriptionID].strDates["#DateFormat(local.thisDateAmt.date,'m_d_yyyy')#"])>

								SET @couponID = #int(val(local.strCoupon.couponID))#;
								SET @discountAmount = #local.thisSubDiscount.discount# * - 1;
								SET @redemptionCount = #int(val(local.thisSubDiscount.redemptionCount))#;
								
								EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
									@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@transDate, 
									@saleTransactionID=@subTransactionID, @invoiceID=@invoiceID_#local.thisCurrTransaction.invoiceCount#, 
									@couponID=@couponID, @itemType='SubReg', @itemID=@subscriberIDForSub#local.thisCurrTransaction.subscriptionID#, 
									@redemptionCount=@redemptionCount, @transactionID=@adjTransactionID OUTPUT;
							</cfif>

						<cfelseif local.thisCurrTransaction.action eq "Adjust" and decimalFormat(local.thisCurrTransaction.adjustDifference) neq 0.00>
							set @XMLSchedule = null;
							<cfif local.thisCurrTransaction.adjustDifference gt 0>
								<cfset local.strTaxIndiv = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.thisCurrTransaction.GLAccountID, 
																saleAmount=local.thisCurrTransaction.adjustDifference, transactionDate=arguments.transdate, stateIDForTax=val(arguments.stateIDforTax),
																zipForTax=arguments.zipForTax)>
								SELECT @XMLSchedule = (
									select row.amt, convert(varchar(10),row.dt,101) as dt
									from @tblFinalDefSchedule as row
									inner join @tblRecognitions as r on r.subscriptionID = row.subscriptionID and r.subscriptionTransOrder = row.subscriptionTransOrder
									where r.subscriptionID = #local.thisCurrTransaction.subscriptionID#
									and r.dateamtInvNum = #local.thisDateAmt.profiles[local.thisProfileKey].invoicenumber#
									and r.dateAmtTransNumber = #local.thisCurrTransactionNum#
									for XML AUTO, ROOT('rows'), TYPE
								);
							</cfif>

							EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, @statsSessionID=@statsSessionID,
								@amount=#local.thisCurrTransaction.adjustDifference#, @taxAmount=<cfif local.thisCurrTransaction.adjustDifference gt 0>#val(local.strTaxIndiv.totalTaxAmt)#<cfelse>null</cfif>,
								@transactionDate=@transDate, @autoAdjustTransactionDate=1, @saleTransactionID=#local.thisCurrTransaction.saleTransactionID#, 
								@invoiceID=<cfif local.thisCurrTransaction.invoiceCount neq 0>@invoiceID_#local.thisCurrTransaction.invoiceCount#<cfelse>#local.thisCurrTransaction.invoiceID#</cfif>, 
								@byPassTax=0, @byPassAccrual=0, @xmlSchedule=@XMLSchedule, @transactionID=@trashID OUTPUT;
						</cfif>
					</cfloop>
				</cfloop>
			</cfloop>

			<cfset local.arrCount = 0>
			<cfset local.isFirstPaymentDate = true>
			<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
				<cfloop list="#arguments.listInvoiceProfileIDs#" index="local.thisInvProfileID">
					<cfif StructKeyExists(local.thisDateAmt, "profiles") AND StructKeyExists(local.thisDateAmt.profiles, "#local.thisInvProfileID#")>
						<cfif Len(local.thisDateAmt.profiles["#local.thisInvProfileID#"].amt) gt 0 
								and not local.isFirstPaymentDate 
								and arguments.strEventCollection['hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice'] eq true>
							<cfset local.arrCount = local.arrCount + 1>
		
							update dbo.tr_invoices
							set statusID = 2
							where invoiceID = @invoiceID_#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber#;
	
							insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (@invoiceID_#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber#, getdate(), 2, 1, @loggedInMemberID);
						<cfelse>
							<cfif local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID gt 0>
								exec dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceID#;
							<cfelse>					
								<cfset local.arrCount = local.arrCount + 1>
								<cfif listFind(arguments.thisInvoiceIDList,local.arrCount)>
									exec dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceID_#local.arrCount#;
								</cfif>
							</cfif>
						</cfif>
					</cfif>
				</cfloop>
				<cfset local.isFirstPaymentDate = false>
			</cfloop>	
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.retString>
	</cffunction>

	<cffunction name="updateSubscriptionTransactionsDeferred" access="private" output="false" returntype="array">
		<cfargument name="dateAmts" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.dateAmts = arguments.dateAmts>
		<cfset local.strDeferredAccounts = structNew()>

		<cftry>
			<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
				<cfloop collection="#local.thisDateAmt.profiles#" item="local.thisProfileKey">
					<cfloop array="#local.thisDateAmt.profiles[local.thisProfileKey].transactions#" index="local.thisCurrTransaction">
						<cfif structKeyExists(local.thisCurrTransaction,"GLAccountID") and local.thisCurrTransaction.GLAccountID gt 0>
							<cfif structKeyExists(local.strDeferredAccounts,local.thisCurrTransaction.GLAccountID)>
								<cfset local.thisCurrTransaction.deferredGLAccountID = local.strDeferredAccounts[local.thisCurrTransaction.GLAccountID]>
							<cfelse>	
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeferred">
									select dbo.fn_tr_getDeferredGLAccountID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCurrTransaction.GLAccountID#">) as deferredGLAccountID
								</cfquery>
								<cfset local.thisCurrTransaction.deferredGLAccountID = val(local.qryDeferred.deferredGLAccountID)>
								<cfset structInsert(local.strDeferredAccounts, local.thisCurrTransaction.GLAccountID, val(local.qryDeferred.deferredGLAccountID))>
							</cfif>
						</cfif>
					</cfloop>
				</cfloop>
			</cfloop>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfrethrow>
		</cfcatch>
		</cftry>

		<cfreturn local.dateAmts>
	</cffunction>

	<cffunction name="getSubscriptionTransactions" access="private" output="false" returntype="array">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="numPayments" type="numeric" required="true">
		<cfargument name="subEndDate" type="string" required="true">
		<cfargument name="dateAmts" type="array" required="true">
		<cfargument name="qryExistingInvoices" type="any" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.retVal = structNew()>		

		<cftry>
			<cfset local.retVal.dateAmts = arguments.dateAmts>
			<cfset local.qryExistingInvoices = arguments.qryExistingInvoices>
			<cfset local.subAmts = getSubscriptionAmounts(subXML=arguments.subXML, numPaymentsToUse=arguments.numPayments, includeExtraSubInfo=true)>
			<cfloop array="#local.subAmts.payOrderArray#" index="local.thisPayOrderEntry">
				<cfif local.thisPayOrderEntry.payOrder eq "0000."> <!--- must be put on first available invoice for invoice profile --->
					<cfif ArrayLen(local.retVal.dateAmts) gt 0>
						<cfset local.thisDateAmt = local.retVal.dateAmts[1]>
						
						<cfloop array="#local.thisPayOrderEntry.profiles#" index="local.thisPayOrderProfileEntry">
							<cfloop array="#local.thisPayOrderProfileEntry.subs#" index="local.thisSubEntry">
								<cfif (local.thisSubEntry.subscriberID eq 0) OR
											((local.thisSubEntry.subscriberID neq 0) 
												AND (local.thisSubEntry.subStatus eq 'R' 
															OR local.thisSubEntry.subStatus eq 'O'
															OR ((local.thisSubEntry.reAddMe eq 1) AND (local.thisSubEntry.subHasInvoiceID neq 'true'))))> <!--- it's a new add --->
									<cfset local.currDetail = local.thisSubEntry.name>
									<cfset local.currTransaction = StructNew()>
									<cfset local.currTransaction.action = 'Add'>
									<cfset local.currTransaction.subEndDate = arguments.subEndDate>
									<cfset local.currTransaction.detail = local.currDetail>
									<cfset local.currTransaction.amount = local.thisSubEntry.total>
									<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
									<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
									<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
									<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
	
									<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
									<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.thisSubEntry.total>	
									<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.thisSubEntry.total>
									<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.thisSubEntry.total>
									<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.thisSubEntry.total>
	
								<cfelse> <!--- it's an add or adjust, but with calculations --->
									<cfif (local.thisSubEntry.subscriberID eq 0) OR
												((local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID eq 0) AND 
													(local.thisSubEntry.subHasInvoiceID neq 'true'))> <!--- add --->
	
										<cfset local.currDetail = local.thisSubEntry.name>
										<cfset local.currTransaction = StructNew()>
										<cfset local.currTransaction.action = 'Add'>
										<cfset local.currTransaction.subEndDate = arguments.subEndDate>
										<cfset local.currTransaction.detail = local.currDetail>
										<cfset local.currTransaction.amount = local.thisSubEntry.total>
										<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
										<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
										<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
										<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
		
										<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
										<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.thisSubEntry.total>	
										<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.thisSubEntry.total>
										<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.thisSubEntry.total>
										<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.thisSubEntry.total>
	
									<cfelse> <!--- adjust --->
										<!--- this is an existing invoice and existing transaction --->
										<!--- get the previous transaction + adjustments made to the transaction for this subscription --->
	
										<cfquery dbtype="query" name="local.subPreviousAmt">
											select mainTransactionID as transactionID, sum(totalRemaining) as totalRemaining
											from [local].qryExistingInvoices
											where invoiceID = #local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID#
											and subscriberID = #local.thisSubEntry.subscriberID#
											group by invoiceID, mainTransactionID
										</cfquery>
										<cfquery name="local.subPreviousAmtGL" datasource="#application.dsn.membercentral.dsn#">
											select creditGLAccountID
											from dbo.tr_transactions
											where transactionID = #val(local.subPreviousAmt.transactionID)#
										</cfquery>

										<cfset local.subAdjustDifference = local.thisSubEntry.total - val(local.subPreviousAmt.totalRemaining)>
	
										<cfset local.currTransaction = StructNew()>
										<cfset local.currTransaction.action = 'Adjust'>
										<cfset local.currTransaction.amount = local.thisSubEntry.total>
										<cfset local.currTransaction.GLAccountID = local.subPreviousAmtGL.creditGLAccountID>
										<cfset local.currTransaction.adjustDifference = local.subAdjustDifference>
										<cfset local.currTransaction.saleTransactionID = local.subPreviousAmt.transactionID>
										<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
										<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
										<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
	
										<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>			
										<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.thisSubEntry.total>	
										<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.thisSubEntry.total>
										<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.thisSubEntry.total>
										<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.thisSubEntry.total>
	
									</cfif>
								</cfif>
							</cfloop>
						</cfloop>
						
					</cfif>
				<cfelse> <!--- work the invoices in order --->
				
					<cfloop array="#local.retVal.dateAmts#" index="local.thisDateAmt">
						<cfloop array="#local.thisPayOrderEntry.profiles#" index="local.thisPayOrderProfileEntry">
							
							<cfif StructKeyExists(local.thisDateAmt.profiles, "#local.thisPayOrderProfileEntry.ID#")>
								
								<cfset local.currPayOrderProfileAmountRemaining = local.thisPayOrderProfileEntry.total - local.thisPayOrderProfileEntry.totalApplied>
								
								<cfset local.currInvoiceAmountRemaining = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].amt - local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal>
								
								<cfif local.currInvoiceAmountRemaining gt 0>
	
									<cfset local.poSubsAmtApplied = 0.00>
									<cfset local.subArrayCount = 0>
									<cfloop array="#local.thisPayOrderProfileEntry.subs#" index="local.thisSubEntry">
										<cfset local.subArrayCount = local.subArrayCount + 1>
			
										<cfset local.currSubAmountRemaining = local.thisSubEntry.total - local.thisSubEntry.totalApplied>
	
										<cfif local.currInvoiceAmountRemaining gte local.currPayOrderProfileAmountRemaining>
											<cfset local.currUsePayment = local.currSubAmountRemaining>
										<cfelse>
											<!--- partial payments --->
											<cfset local.thisSubEntry.isSinglePayment = false>
											<cfset local.currUsePayment = numberformat((local.thisSubEntry.total / local.thisPayOrderProfileEntry.total) * local.currInvoiceAmountRemaining, "_.__")>
											<cfif local.currInvoiceAmountRemaining lt (local.currUsePayment + local.poSubsAmtApplied)>
												<cfset local.currUsePayment = local.currUsePayment - ((local.currUsePayment + local.poSubsAmtApplied) - local.currInvoiceAmountRemaining)>
											<cfelseif (ArrayLen(local.thisPayOrderProfileEntry.subs) eq local.subArrayCount) AND
																(local.currPayOrderProfileAmountRemaining gt (local.currUsePayment + local.poSubsAmtApplied)) AND
																(local.currInvoiceAmountRemaining gt (local.currUsePayment + local.poSubsAmtApplied)) AND
																(local.currSubAmountRemaining gt (local.currUsePayment + (local.currInvoiceAmountRemaining - (local.currUsePayment + local.poSubsAmtApplied))))>
												<cfset local.currUsePayment = local.currUsePayment + (local.currInvoiceAmountRemaining - (local.currUsePayment + local.poSubsAmtApplied))>
											</cfif> <!--- end if/elseif local.currInvoiceAmountRemaining lt (local.currUsePayment + local.poSubsAmtApplied) --->
										</cfif> <!--- end if/else local.currInvoiceAmountRemaining gte local.currPayOrderProfileAmountRemaining --->
	
										<cfif local.thisSubEntry.isSinglePayment eq "true">
											<cfset local.currDetail = local.thisSubEntry.name>
										<cfelse>
											<cfset local.currDetail = "#local.thisSubEntry.name# Installment">
										</cfif> <!--- end if/else local.thisSubEntry.isSinglePayment eq "true" --->
										
										<cfif (local.thisSubEntry.subscriberID eq 0) OR
													((local.thisSubEntry.subscriberID neq 0) 
														AND (local.thisSubEntry.subStatus eq 'R' 
																	OR local.thisSubEntry.subStatus eq 'O'
																	OR ((local.thisSubEntry.reAddMe eq 1) AND (local.thisSubEntry.subHasInvoiceID neq 'true'))))> <!--- it's a new add --->
	
											<cfif (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0)>
												<cfset local.currTransaction = StructNew()>
												<cfset local.currTransaction.action = 'Add'>
												<cfset local.currTransaction.subEndDate = arguments.subEndDate>
												<cfset local.currTransaction.detail = local.currDetail>
												<cfset local.currTransaction.amount = local.currUsePayment>
												<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
												<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
												<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
												<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
				
												<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
												<cfset local.poSubsAmtApplied = local.poSubsAmtApplied + local.currUsePayment>
												<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.currUsePayment>	
												<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.currUsePayment>
												<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.currUsePayment>
												<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.currUsePayment>
	
											<cfelse>
												<cfset local.currDetail = local.thisSubEntry.name>
												<cfif local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID gt 0>
													<cfset local.thisInvoiceCountToUse = 0>
												<cfelse>
													<cfset local.thisInvoiceCountToUse = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
												</cfif>
												
												<cfset local.currTransaction = StructNew()>
												<cfset local.currTransaction.action = 'Add'>
												<cfset local.currTransaction.subEndDate = arguments.subEndDate>
												<cfset local.currTransaction.detail = local.currDetail>
												<cfset local.currTransaction.amount = 0>
												<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
												<cfset local.currTransaction.invoiceCount = local.thisInvoiceCountToUse>
												<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
												<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
				
												<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
											</cfif> <!--- end if/else (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0) --->
	
										<cfelse> <!--- it's an add or adjust, but with calculations --->
										
											<cfif (local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID eq 0) 
															OR (local.thisSubEntry.subscriberID eq 0)> <!--- add --->
															
												<cfif (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0)>
													<cfset local.currTransaction = StructNew()>
													<cfset local.currTransaction.action = 'Add'>
													<cfset local.currTransaction.subEndDate = arguments.subEndDate>
													<cfset local.currTransaction.detail = local.currDetail>
													<cfset local.currTransaction.amount = local.currUsePayment>
													<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
													<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
													<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
													<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
					
													<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
													<cfset local.poSubsAmtApplied = local.poSubsAmtApplied + local.currUsePayment>
													<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.currUsePayment>	
													<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.currUsePayment>
													<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.currUsePayment>
													<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.currUsePayment>
	
												<cfelseif local.thisSubEntry.subscriberID eq 0>		
													<cfset local.currDetail = local.thisSubEntry.name>
													<cfif local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID gt 0>
														<cfset local.thisInvoiceCountToUse = 0>
													<cfelse>
														<cfset local.thisInvoiceCountToUse = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
													</cfif>
													
													<cfset local.currTransaction = StructNew()>
													<cfset local.currTransaction.action = 'Add'>
													<cfset local.currTransaction.subEndDate = arguments.subEndDate>
													<cfset local.currTransaction.detail = local.currDetail>
													<cfset local.currTransaction.amount = 0>
													<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
													<cfset local.currTransaction.invoiceCount = local.thisInvoiceCountToUse>
													<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
													<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
					
													<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
												</cfif> <!--- end if/elseif (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0) --->
											<cfelse> <!--- adjust --->
												<!--- this is an existing invoice and existing transaction --->
												<!--- get the previous transaction + adjustments made to the transaction for this subscription --->
												<cfquery dbtype="query" name="local.subPreviousAmt">
													select mainTransactionID as transactionID, sum(totalRemaining) as totalRemaining
													from [local].qryExistingInvoices
													where invoiceID = #local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID#
													and subscriberID = #local.thisSubEntry.subscriberID#
													group by invoiceID, mainTransactionID
												</cfquery>
											
												<cfif val(local.subPreviousAmt.transactionID) eq 0>
													<cfif (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0)>
														<!--- invoice existed, but this subscription (which already exists) wasn't on it --->
														<cfset local.currTransaction = StructNew()>
														<cfset local.currTransaction.action = 'Add'>
														<cfset local.currTransaction.subEndDate = arguments.subEndDate>
														<cfset local.currTransaction.detail = local.currDetail>
														<cfset local.currTransaction.amount = local.currUsePayment>
														<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
														<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
														<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
														<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
						
														<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
														<cfset local.poSubsAmtApplied = local.poSubsAmtApplied + local.currUsePayment>
														<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.currUsePayment>	
														<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.currUsePayment>
														<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.currUsePayment>
														<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.currUsePayment>
	
													</cfif> <!--- end if (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0) --->
												<cfelse>
													<cfquery name="local.subPreviousAmtGL" datasource="#application.dsn.membercentral.dsn#">
														select creditGLAccountID
														from dbo.tr_transactions
														where transactionID = #val(local.subPreviousAmt.transactionID)#
													</cfquery>

													<cfset local.subAdjustDifference = local.currUsePayment - val(local.subPreviousAmt.totalRemaining)>
													
													<cfset local.currTransaction = StructNew()>
													<cfset local.currTransaction.action = 'Adjust'>
													<cfset local.currTransaction.amount = local.currUsePayment>
													<cfset local.currTransaction.GLAccountID = local.subPreviousAmtGL.creditGLAccountID>
													<cfset local.currTransaction.adjustDifference = local.subAdjustDifference>
													<cfset local.currTransaction.saleTransactionID = local.subPreviousAmt.transactionID>
													<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
													<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
													<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
			
													<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>			
													<cfset local.poSubsAmtApplied = local.poSubsAmtApplied + local.currUsePayment>
													<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.currUsePayment>	
													<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.currUsePayment>
													<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.currUsePayment>
													<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.currUsePayment>
	
												</cfif> <!--- end if/else val(local.subPreviousAmt.transactionID) eq 0 --->
											
											</cfif> <!--- end if/else (local.thisDateAmt.invoiceID eq 0) OR (local.thisSubEntry.subscriberID eq 0) --->
										
										</cfif> <!--- end if/else new or existing --->
									</cfloop> <!--- end local.thisPayOrderProfileEntry.subs loop --->							
								<cfelse>
									<!--- invoice amt has been met, if there is a previous amount, it needs to be removed  --->
									<cfloop array="#local.thisPayOrderProfileEntry.subs#" index="local.thisSubEntry">
										<cfset local.trNew = true>
										<cfif (local.thisSubEntry.subscriberID eq 0)>
											<cfset local.trNew = true>
										<cfelse>
											<cfquery dbtype="query" name="local.subPreviousAmt">
												select mainTransactionID as transactionID, sum(totalRemaining) as totalRemaining
												from [local].qryExistingInvoices
												where invoiceID = #local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID#
												and subscriberID = #local.thisSubEntry.subscriberID#
												group by invoiceID, mainTransactionID
											</cfquery>
											<cfif (val(local.subPreviousAmt.transactionID) eq 0)>
												<cfset local.trNew = true>
											<cfelse>
												<cfset local.trNew = false>
											</cfif> <!--- end if/else (val(local.subPreviousAmt.transactionID) eq 0) --->
										</cfif> <!--- end if/else (local.thisSubEntry.subscriberID eq 0) --->
	
										<cfif local.trNew>
											<cfset local.currDetail = local.thisSubEntry.name>
											<cfif local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID gt 0>
												<cfset local.thisInvoiceCountToUse = 0>
											<cfelse>
												<cfset local.thisInvoiceCountToUse = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
											</cfif> <!--- end if/else local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID gt 0 --->
											
											<cfset local.currTransaction = StructNew()>
											<cfset local.currTransaction.action = 'Add'>
											<cfset local.currTransaction.subEndDate = arguments.subEndDate>
											<cfset local.currTransaction.detail = local.currDetail>
											<cfset local.currTransaction.amount = 0>
											<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
											<cfset local.currTransaction.invoiceCount = local.thisInvoiceCountToUse>
											<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
											<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
			
											<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
										<cfelse>
											<cfif val(local.subPreviousAmt.totalRemaining) gt 0>
												<cfquery name="local.subPreviousAmtGL" datasource="#application.dsn.membercentral.dsn#">
													select creditGLAccountID
													from dbo.tr_transactions
													where transactionID = #val(local.subPreviousAmt.transactionID)#
												</cfquery>

												<cfset local.currTransaction = StructNew()>
												<cfset local.currTransaction.action = 'Adjust'>
												<cfset local.currTransaction.amount = 0>
												<cfset local.currTransaction.GLAccountID = local.subPreviousAmtGL.creditGLAccountID>
												<cfset local.currTransaction.adjustDifference = local.subPreviousAmt.totalRemaining * -1>
												<cfset local.currTransaction.saleTransactionID = local.subPreviousAmt.transactionID>
												<cfset local.currTransaction.invoiceCount = 0>
												<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
												<cfset local.currTransaction.invoiceID = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceID>
				
												<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>			
											</cfif> <!--- end if val(local.subPreviousAmt.totalRemaining) gt 0 --->
										</cfif> <!--- end if/else local.trNew --->
											
									</cfloop> <!--- end local.thisPayOrderProfileEntry.subs loop --->							
									
								</cfif> <!--- end if/else local.currInvoiceAmountRemaining gt 0 --->
								
							</cfif> <!--- if StructKeyExists(local.thisDateAmt.profiles, "#local.thisPayOrderProfileEntry.ID#") --->
						
						</cfloop> <!--- end local.thisPayOrderEntry.profiles loop --->
					</cfloop>	<!--- end local.retVal.dateAmts loop --->
				
				</cfif> <!--- end if/else local.thisPayOrderEntry.payOrder eq "0000." --->
			</cfloop> <!--- end local.subAmts.payOrderArray loop --->

			<!--- get the deferred GLs if there are any --->
			<!--- For this to work, all sales and positive adjustments must have a GLAccountID identified above --->
			<cfset local.retVal.dateAmts = updateSubscriptionTransactionsDeferred(dateAmts=local.retVal.dateAmts)>

		<cfcatch type="any">
			<cfset local.arguments = arguments />
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="exception in model.admin.subscriptions.subscriptionReg.getSubscriptionTransactions + rethrow")>
			<cfrethrow>
		</cfcatch>
		</cftry>

		<cfreturn local.retVal.dateAmts>
	</cffunction>
	
	<cffunction name="doSubscribeEdit" access="public" output="false" returntype="string">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cftry>
			<cfset arguments.event.paramValue('subAction','')>
			<cfset arguments.event.paramValue('mid','0')>
			<cfset local.puid = arguments.event.getValue('puid','')>
			<cfset local.dspStep = "">
			<cfset local.showTreePrices = false>
			<cfset local.subStartTermDate = "">
			<cfset local.subEndTermDate = "">
			<cfset local.graceEndTermDate = "">
			<cfset local.termDateString = "">
			<cfset local.prevTermDateString = "">
			
			<cfset local.objSubscriptions = createObject("component","model.admin.subscriptions.subscriptions")>
			<cfset local.maxFrequencyInstallments = getMaxFrequencyInstallments(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

			<!--- load any subxml in progress --->
			<cfset local.xmlSubscribeMember = loadSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'))>
		
			<cfif XMLSearch(local.xmlSubscribeMember,"count(//set[@id='0']/subscription)") gt 0>
				<cfset local.rootSubscriberID = xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@sid)")>
				<cfset local.topRFID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@rfid)"))>
				<cfset local.topStatus = xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@currstatus)")>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRenewal">
					select r.isRenewalRate
					from dbo.sub_rates r
					inner join dbo.sub_rateFrequencies rf
						on rf.rateID = r.rateID
						and rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topRFID#">
				</cfquery>
				<cfif ((local.topStatus eq "R") OR (local.topStatus eq "O")) AND (val(local.qryRenewal.isRenewalRate) eq 1)>
					<cfset local.isRenewal = true>
				<cfelse>
					<cfset local.isRenewal = false>
				</cfif>
				<cfset local.fakeisRenewal = false>
				<cfset local.subTermStartDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
				<cfset local.subTermEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>

				<cfif (len(local.subTermStartDate) eq 0) OR (len(local.subTermEndDate) eq 0)>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
						select subStartDate, subEndDate, graceEndDate
						from dbo.sub_subscribers
						where subscriberID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@sid)")#">
					</cfquery>
					
					<cfset local.subTermStartDate = DateFormat(local.qrySubDates.subStartDate, "mm/dd/yyyy")>
					<cfset local.subTermEndDate = DateFormat(local.qrySubDates.subEndDate, "mm/dd/yyyy")>
				</cfif>				

				<cfset local.termDateString = "#DateFormat(local.subTermStartDate, 'm/d/yyyy')# - #DateFormat(local.subTermEndDate, 'm/d/yyyy')#">
				
				<cfset local.prevTermSubID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@sid)"))>
				<cfset local.prevTermDates = getPrevSubTermDates(local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, local.prevTermSubID)>			
				<cfif (local.prevTermDates.success eq true) AND (len(local.prevTermDates.subTermStartDate)) AND (len(local.prevTermDates.subTermEndDate))>
					<cfset local.prevTermDateString = "#DateFormat(local.prevTermDates.subTermStartDate, 'm/d/yyyy')# - #DateFormat(local.prevTermDates.subTermEndDate, 'm/d/yyyy')#">
				</cfif>
			</cfif>
			
			<!--- update pointer if necessary --->
			<cfif len(local.puid) and local.puid neq xmlSearch(local.xmlSubscribeMember,"string(//process/@pointer)")>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.pointer = local.puid>
			</cfif>

			<cfswitch expression="#arguments.event.getValue('subAction')#">

				<cfcase value="chooseSub">
					<cfset local.pointerNode = xmlSearch(local.xmlSubscribeMember,"//node()[@uid = '#local.puid#']")>
					<cfset local.memberID = local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid>

					<!--- if pointer is the first set (set with id=0) and there is no addonID, adding a root subscription --->
					<cfif arraylen(local.pointerNode) and local.pointerNode[1].xmlName eq "set" and local.pointerNode[1].xmlAttributes.id is 0 and NOT arguments.event.valueExists('aoid')>
						<cfquery name="local.qrySubName" datasource="#application.dsn.membercentral.dsn#">
							select top 1 s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.paymentOrder, s.allowRateGLAccountOverride,
								o.subActivationCode, o2.subActivationCode as subAlternateActivationCode
							from dbo.sub_subscriptions s
							inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
							inner join dbo.sub_activationOptions o2 on o2.subActivationID = s.subAlternateActivationID
							where s.subscriptionID = <cfqueryparam value="#val(arguments.event.getValue('subid',0))#" cfsqltype="CF_SQL_INTEGER">
							and s.soldSeparately = 1
							and s.status = 'A'
						</cfquery>

						<cfif local.qrySubName.recordcount is 0>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&err=CS.NS" addtoken="no">
						<cfelseif xmlSearch(local.pointerNode[1],"count(//subscription[@id = '#local.qrySubName.subscriptionID#'])") gt 0>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showRates&puid=#local.puid#" addtoken="no">
						<cfelse>
							<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.puid, subid=local.qrySubName.subscriptionID, 
									subname=local.qrySubName.subscriptionName, subTermFlag=local.qrySubName.rateTermDateFlag, GLAccountIDToUse=local.qrySubName.GLAccountID, 
									allowRateGLOverride=local.qrySubName.allowRateGLAccountOverride, payOrder=local.qrySubName.paymentOrder, 
									activationOptionCode=local.qrySubName.subActivationCode, alternateActivationOptionCode=local.qrySubName.subAlternateActivationCode)>
							<cfif len(local.strAddSubResult.subUID)>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.memberID, subXML=toString(local.strAddSubResult.subXML))>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showRates&puid=#local.strAddSubResult.subUID#" addtoken="no">
							<cfelse>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&err=CS.NAS" addtoken="no">
							</cfif>
						</cfif>

					<!--- else if pointer is a sub and there is an addonID --->
					<cfelseif arraylen(local.pointerNode) and local.pointerNode[1].xmlName eq "subscription" and arguments.event.valueExists('aoid')>
						<cfquery name="local.qrySubName" datasource="#application.dsn.membercentral.dsn#">
							select ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.allowRateGLAccountOverride,
								o.subActivationCode, o2.subActivationCode as subAlternateActivationCode, sets.setid, sets.setName,
								ao.useAcctCodeInSet, ao.PCnum, ao.PCPctOffEach, ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice,
								s.paymentOrder as subPayOrder
							from dbo.sub_addons as ao
							inner join dbo.sub_sets as sets
								on sets.setID = ao.childSetID
								and ao.addOnID = <cfqueryparam value="#val(arguments.event.getValue('aoid',0))#" cfsqltype="CF_SQL_INTEGER">
								and ao.subscriptionID = <cfqueryparam value="#val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@id)"))#" cfsqltype="CF_SQL_INTEGER">
							INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
							INNER JOIN dbo.sub_subscriptions as s
								on s.subscriptionID = ss.subscriptionID and s.status = 'A'
								and s.subscriptionID = <cfqueryparam value="#val(arguments.event.getValue('subid',0))#" cfsqltype="CF_SQL_INTEGER">
							INNER JOIN dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
							INNER JOIN dbo.sub_activationOptions o2 on o2.subActivationID = s.subAlternateActivationID

						</cfquery>

						<cfset local.alreadySub = false>				
						<cfset local.alreadyFree = false>				
						<!--- 
							check to see if the item being added was already a part of the subscription (deleted then added back in the same window)
							If so, add it as "alreadysub" = true
							and if alreadysub eq true, don't go to rates
						--->
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriber">
							select subscriberID, subscriptionID, typeName, subscriptionName, status, RFID, subStartDate, subEndDate, graceEndDate, PCFree,parentSubscriberID, thePath, thePathExpanded
							from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">)
							where status <> 'D'
							and subscriptionID = <cfqueryparam value="#val(arguments.event.getValue('subid',0))#" cfsqltype="CF_SQL_INTEGER">
						</cfquery>
						
						<cfif local.qrySubscriber.recordcount gt 0>
							<cfset local.alreadySub = true>			
							<cfif local.qrySubscriber.PCFree gt 0>	
								<cfset local.alreadyFree = true>
							</cfif>
						</cfif>
					
						<cfif local.qrySubName.recordcount is 0>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&err=CS.NSAO" addtoken="no">
						<cfelse>
							<cfset local.strAddSetResult = addSet(subXML=local.xmlSubscribeMember, parentUID=local.puid, setid=local.qrySubName.setid, setname=local.qrySubName.setname, feAllowSelect=local.qrySubName.frontEndAllowSelect, feAllowChangePrice=local.qrySubName.frontEndAllowCHangePrice, feAddAdditional=local.qrySubName.frontEndAddAdditional)>
							<cfif len(local.strAddSetResult.setUID)>
								<cfset local.xmlSubscribeMember = local.strAddSetResult.subXML>
								
								<!--- get the number of free subs added to determine if this one is free --->
								<cfset local.subFreeCount = XMLSearch(local.xmlSubscribeMember,"count(//subscription[@uid='#local.puid#']/set[@uid='#local.strAddSetResult.setUID#']/subscription[@pcisfree='true'])")>	

								<cfif (local.alreadyFree eq true) OR ((local.alreadySub eq false) AND (local.qrySubName.PCNum gt local.subFreeCount))>
									<cfset local.currSubIsFree = true>
								<cfelse>
									<cfset local.currSubIsFree = false>
								</cfif>
								
								<cfif local.qrySubName.useAcctCodeInSet eq 0>
									<!--- use the parent GLAccountID --->
									<cfset local.currGLAToUse = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@glaccountidtouse)"))>
								<cfelse>
									<cfset local.currGLAToUse = local.qrySubName.GLAccountID>
								</cfif>

								<cfset local.parentStatus = xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@currstatus)")>

								<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.strAddSetResult.setUID, 
									subid=local.qrySubName.subscriptionID, subname=local.qrySubName.subscriptionName, subTermFlag=local.qrySubName.rateTermDateFlag,
									GLAccountIDToUse=local.currGLAToUse, allowRateGLOverride=local.qrySubName.allowRateGLAccountOverride, pcnumfree=local.qrySubName.PCnum,
									pcpctoff=local.qrySubName.PCPctOffEach, alreadySub=local.alreadySub, pcfree=local.currSubIsFree, currstatus=local.parentStatus, 
									payOrder=local.qrySubName.subPayOrder, activationOptionCode=local.qrySubName.subActivationCode,
									alternateActivationOptionCode=local.qrySubName.subAlternateActivationCode)>

								<cfif len(local.strAddSubResult.subUID)>
									<cfset local.strUpdateFreeSubsResult = updateFreeSubs(subXML=local.strAddSubResult.subXML, setUID=local.strAddSetResult.setUID, setParentUID=local.puid)>
									<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.memberID, subXML=toString(local.strUpdateFreeSubsResult.subXML))>
									<cfif (local.alreadySub eq false) OR (local.parentStatus eq 'R') OR (local.parentStatus eq 'O')>
										<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showRates&puid=#local.strAddSubResult.subUID#" addtoken="no">
									<cfelse>
										<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#" addtoken="no">
									</cfif>
								</cfif>

							<cfelse>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&err=CS.NASS" addtoken="no">
							</cfif>
						</cfif>

					<!--- else no bueno --->
					<cfelse>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&err=CS.NP" addtoken="no">
					</cfif>
				</cfcase>
				
				<cfcase value="removeSub">
					<cfset local.subUID = arguments.event.getValue('subuid','')>	

					<cfset local.setUID = XMLSearch(local.xmlSubscribeMember,"string(//subscription[@uid='#local.subUID#']/../@uid)")>	
					<cfset local.setParentUID = XMLSearch(local.xmlSubscribeMember,"string(//subscription[@uid='#local.subUID#']/../../@uid)")>	
					<cfset local.returnSetID = XMLSearch(local.xmlSubscribeMember,"string(//subscription[@uid='#local.subUID#']/../@id)")>	
					<cfset local.strRemoveSubResult = removeSub(subXML=local.xmlSubscribeMember, subUID=local.subUID)>
					<cfset local.strUpdateFreeSubsResult = updateFreeSubs(subXML=local.strRemoveSubResult.subXML, setUID=local.setUID, setParentUID=local.setParentUID)>
					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.strUpdateFreeSubsResult.subXML))>
					
					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddOns&puid=#local.puid#&removeSub=1&setID=#local.returnSetID#" addtoken="no">
				</cfcase>
				
				<cfcase value="showRates">
					<cfif xmlsearch(local.xmlSubscribeMember,"count(//set/subscription[@uid = '#local.puid#'])") gt 0>
						<!--- if this sub uses rates (an addon setting), get them --->
						<cfset local.topUID = XMLSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@uid)")>
						<cfset local.origFreqID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@freqid)"))>
						<cfset local.subUseRates = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@userates)")>
						<cfset local.subOrigRFID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@origRFID)")>
						<cfset local.subLastPrice = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@lastPrice)")>
						<cfset local.subContainingSet = (XMLSearch(local.xmlSubscribeMember,"//set[subscription[@uid='#local.puid#']][1]"))>

						<cfset local.qryRates = getRateQuery(memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid,
								siteID=arguments.event.getValue('mc_siteinfo.siteid'),
								isRoot=(local.topUID eq local.puid),
								subID=val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@id)")),
								currFreqID=local.origFreqID,
								currRateID=val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@rateID)")),
								currRFID=val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@rfid)")),
								isRenewal=local.isRenewal,
								autoSelectRateMatch=false)>
						<!--- if only one rate, auto select it (do what chooseRate does) --->
						<cfif local.qryRates.recordcount is 1>
							<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.puid, rfid=local.qryRates.rfid)>
							<cfif local.topUID eq local.puid>
								<cfset local.strUpdateRateResult = updateRates(subXML=local.strAddRateResult.subXML, origFreqID=local.origFreqID, isRenewal=local.isRenewal, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, subXML=toString(local.strUpdateRateResult.subXML))>
							<cfelse>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, subXML=toString(local.strAddRateResult.subXML))>
							</cfif>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.puid#" addtoken="no">
						<cfelse>
							<cfset local.subPCPctOff = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@pcpctoff)")>
							<cfset local.subPCRateAmt = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@pcrateamt)")>
							<cfset local.subPCFree = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@pcisfree)")>
							<cfset local.subName = xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@name)")>
							<cfset local.currSetID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/../@id)")>
							<cfset local.dspStep = "subRates">
						</cfif>
					<cfelse>
						<cfset local.dspStep = "invalidData">
					</cfif>
				</cfcase>
				
				<cfcase value="chooseRate">
					<cfset local.topUID = XMLSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@uid)")>
					<cfset local.origFreqID = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@freqid)"))>

					<cfset local.chosenRFID = val(arguments.event.getValue('rfid',0)) />

					<cfset local.priceWasEditable = (arguments.event.valueExists('newRateTotal_#local.chosenRFID#') and arguments.event.valueExists('origRateTotal_#local.chosenRFID#')) />

					<cfif local.priceWasEditable>
						<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.puid, rfid=local.chosenRFID,modifiedRate=val(arguments.event.getValue('newRateTotal_#local.chosenRFID#')))>
					<cfelse>
						<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.puid, rfid=local.chosenRFID)>					
					</cfif>
					<cfif local.topUID eq local.puid>
						<cfset local.strUpdateRateResult = updateRates(subXML=local.strAddRateResult.subXML, origFreqID=local.origFreqID, isRenewal=local.isRenewal, siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.strUpdateRateResult.subXML))>
					<cfelse>
						<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.strAddRateResult.subXML))>
					</cfif>
					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.puid#" addtoken="no">
				</cfcase>
				
				<cfcase value="showAddons">
					<cfset local.chkRates = checkRates(subXML=local.xmlSubscribeMember)>
					<cfset local.subCurrStatus = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@uid = '#local.puid#']/@currstatus)")>
					
					<cfset local.qryAddOnsWithSubs = getAddOnsWithSubs(
						memberID = local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid,
						siteID = arguments.event.getValue('mc_siteinfo.siteid'),
						subscriptionID = val(xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@id)")),
						rootSubscriberID =local.rootSubscriberID,
						isRenewal = local.isRenewal)>
			
		
					<cfset local.listPossible = valueList(local.qryAddOnsWithSubs.subscriptionID)>
					<cfquery name="local.qryAddOns" dbtype="query">
						select addOnID, setid, setname, minAllowed, maxAllowed, frontEndAllowSelect, frontEndAllowChangePrice, frontEndAddAdditional, count(subscriptionid) as SubCount
						from [local].qryAddOnsWithSubs
						group by addOnID, setid, setname, minAllowed, maxAllowed, frontEndAllowSelect, frontEndAllowChangePrice, frontEndAddAdditional
					</cfquery>
					<cfquery name="local.qryFEAddonCounts" dbtype="query">
						select sum(frontEndAllowSelect) as feSelectCount, sum(frontEndAllowChangePrice) as feChangeCount, sum(frontEndAddAdditional) as feAddAdditionalCount
						from [local].qryAddOns
					</cfquery>

					<!--- if no add ons, go back to parent sub (same as doneWithAddOns) --->
					<cfif local.qryAddOnsWithSubs.recordcount is 0>
						<cfset local.parentSubUID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[set/subscription[@uid = '#local.puid#']]/@uid)")>
						<cfif len(local.parentSubUID)>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.parentSubUID#" addtoken="no">
						<cfelse>
							<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showConfirm" addtoken="no">
						</cfif>
					</cfif>
				
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingSubs">
						select s.subscriptionID
						from dbo.sub_subscribers s
						inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
						inner join dbo.sub_types t on t.typeID = subs.typeID and t.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
						inner join dbo.sub_statuses st on st.statusID = s.statusID 
						<cfif (local.subCurrStatus eq 'R') OR (local.subCurrStatus eq 'O') OR (local.subCurrStatus eq 'P')>
							and st.statusCode in ('R','O','P')
						<cfelse>
							and st.statusCode = 'A'
						</cfif>
						where memberID = <cfqueryparam value="#local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid#" cfsqltype="CF_SQL_INTEGER">
						and s.subEndDate > getDate()
					</cfquery>
					<cfset local.listSubscribed = valueList(local.qryExistingSubs.subscriptionID)>				
					
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingSubsInCurrent">
						select subscriberID, subscriptionID, subscriptionName
						from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam value="#local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid#" cfsqltype="CF_SQL_INTEGER">, <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">)
						--where status <> 'D'
						where parentSubscriberID is not null
					</cfquery>
					
					<!--- remove the addon subs from the list of subscribed, since they can be added and deleted in this window--->
					<cfloop query="local.qryExistingSubsInCurrent">
						<cfset local.listNdex = listfind(local.listSubscribed,local.qryExistingSubsInCurrent.subscriptionID)>
						<cfif local.listNdex neq 0>
							<cfset local.listSubscribed = listDeleteAt(local.listSubscribed, local.listNdex)>
						</cfif>
					</cfloop>

					<cfquery dbtype="query" name="local.qryLooseExistingSubsInCurrent">
						select subscriberID, subscriptionID, subscriptionName, 0 as addOnID
						from [local].qryExistingSubsInCurrent
						where subscriptionID not in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.listPossible#">)
					</cfquery>

					
					<!--- If minAllowed = maxAllowed = num of subs in set then auto add them --->
					<cfloop query="local.qryAddOns">
						<cfif local.qryAddOns.minAllowed is local.qryAddOns.maxAllowed and local.qryAddOns.maxAllowed gte local.qryAddOns.SubCount>
							<cfset local.strAddSetResult = addSet(subXML=local.xmlSubscribeMember, parentUID=local.puid, setid=local.qryAddOns.setid, setname=local.qryAddOns.setname, feAllowSelect=local.qryAddOns.frontEndAllowSelect, 
														feAllowChangePrice=local.qryAddOns.frontEndAllowChangePrice, feAddAdditional=local.qryAddOns.frontEndAddAdditional)>
							<cfif len(local.strAddSetResult.setUID)>
								<cfset local.xmlSubscribeMember = local.strAddSetResult.subXML>
								<cfloop query="local.qryAddOnsWithSubs">
									<cfif local.qryAddOns.addOnID is local.qryAddOnsWithSubs.addOnId>
										<cfif local.qryAddOnsWithSubs.useAcctCodeInSet eq 0>
											<cfset local.currGLAToUse = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@glaccountidtouse)"))>
										<cfelse>
											<cfset local.currGLAToUse = local.qryAddOnsWithSubs.GLAccountID>
										</cfif>

										<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.strAddSetResult.setUID, 
											subid=local.qryAddOnsWithSubs.subscriptionID, subname=local.qryAddOnsWithSubs.subscriptionName, 
											subTermFlag=local.qryAddOnsWithSubs.rateTermDateFlag, GLAccountIDToUse=local.currGLAToUse,
											allowRateGLOverride=local.qryAddOnsWithSubs.allowRateGLAccountOverride,
											payOrder=local.qryAddOnsWithSubs.subPayOrder,
											activationOptionCode=local.qryAddOnsWithSubs.subActivationCode, 
											alternateActivationOptionCode=local.qryAddOnsWithSubs.subAlternateActivationCode)>

										<cfset local.xmlSubscribeMember = local.strAddSubResult.subXML>
									</cfif>
								</cfloop>
								<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.xmlSubscribeMember))>
							</cfif>
						</cfif>
					</cfloop>
					
					<cfset local.subName = xmlSearch(local.xmlSubscribeMember,"string(//set/subscription[@uid = '#local.puid#']/@name)")>
					<cfset local.dspStep = "SubAddons">
				</cfcase>
					
				<cfcase value="doneWithAddOns">
					<cfset local.parentSubUID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[set/subscription[@uid = '#local.puid#']]/@uid)")>
					<cfif len(local.parentSubUID)>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.parentSubUID#" addtoken="no">
					<cfelse>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showConfirm" addtoken="no">
					</cfif>
				</cfcase>

				<cfcase value="changePrices">
					<cfset local.subTreeVerboseWithPrices = getSubTreeVerboseWithPrices(subXML=local.xmlSubscribeMember)>
					<cfset local.dspStep = "changePrices">
				</cfcase>
				
				<cfcase value="saveNewPrices">
					<cfloop array="#XMLSearch(local.xmlSubscribeMember,'//subscription')#" index="local.thisSub">
						<cfset local.subIDToUse = Replace(local.thisSub.xmlAttributes.uid, "-", "_", "all")>

						<cfset local.changePriceNew = val(ReReplace(arguments.event.getValue("newRate_#local.subIDToUse#","0"),'[^0-9\.]','','ALL'))>
						<cfset local.changePriceOrig = val(ReReplace(arguments.event.getValue("origRate_#local.subIDToUse#","0"),'[^0-9\.]','','ALL'))>

						<cfif local.changePriceNew neq local.changePriceOrig>
							<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.thisSub.xmlAttributes.uid,
								rfid=val(local.thisSub.xmlAttributes.RFID), modifiedRate=local.changePriceNew, lastPrice=local.thisSub.xmlAttributes.lastPrice,
								origRFID=val(local.thisSub.xmlAttributes.RFID), recogStartDate=local.thisSub.xmlAttributes.recogStartDate,
								recogEndDate=local.thisSub.xmlAttributes.recogEndDate)>
							<cfset local.xmlSubscribeMember = local.strAddRateResult.subXML>
						</cfif>				
					</cfloop>

					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.xmlSubscribeMember))>

					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showConfirm" addtoken="no">
				</cfcase>

				<cfcase value="changeDates">
					<cfset local.dateErrMsg ="">

					<cfset local.dateErrCode = arguments.event.getValue("derr",0)>
					<cfif local.dateErrCode eq 1>
						<cfset local.dateErrMsg = "The dates you have selected would cause subscriptions to overlap. Please enter valid dates that would not overlap with an existing subscription.">
					</cfif>

					<cfset local.subStartDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
					<cfset local.subEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>
					<cfset local.subGraceEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@graceEndDateOverride)")>
					
					<cfif (len(local.subStartDate) eq 0) OR (len(local.subEndDate) eq 0)>
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
							select subStartDate, subEndDate, graceEndDate
							from dbo.sub_subscribers
							where subscriberID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@sid)")#">
						</cfquery>
									
						<cfset local.subStartDate = DateFormat(local.qrySubDates.subStartDate, "m/d/yyyy")>
						<cfset local.subEndDate = DateFormat(local.qrySubDates.subEndDate, "m/d/yyyy")>
						<cfset local.subGraceEndDate = local.qrySubDates.graceEndDate>
						<cfif len(local.subGraceEndDate) gt 0>
							<cfset local.subGraceEndDate = DateFormat(local.subGraceEndDate, "m/d/yyyy")>
						</cfif>
					<cfelse>
						<cfset local.subStartDate = DateFormat(local.subStartDate, "m/d/yyyy")>
						<cfset local.subEndDate = DateFormat(local.subEndDate, "m/d/yyyy")>
						<cfif len(local.subGraceEndDate) gt 0>
							<cfset local.subGraceEndDate = DateFormat(local.subGraceEndDate, "m/d/yyyy")>
						</cfif>
					</cfif>
					
					<cfset local.dspStep = "changeDates">
				</cfcase>

				<cfcase value="saveNewDates">
					<cfset local.newStartDate = arguments.event.getValue("fTermFrom","")>
					<cfset local.newEndDate = arguments.event.getValue("fTermTo","")>
					<cfset local.newGraceEndDate = arguments.event.getValue("fTermGrace","")>
					
					<cfif (Len(local.newStartDate)) gt 0 AND (Len(local.newEndDate) gt 0)>
						
						<!--- check for overlap --->
						<!--- If (New Start Date lte Prev End Date) AND
											(New EndDate gte Prev Start Date)
									{ Overlap }
									
									If (New Grace End Date gte Prev Start Date) AND (New Grace End Date lte Prev End Date (or Prev Grace End Date))
									{ New Grace Overlap }
									
									If (Old Grace End Date gte New Start Date) AND (Old Grace End Date lte New End Date (or New Grace End Date))
									{ Old Grace End Date }
						--->
						<cfset local.prevTermDatesTest = getPrevSubTermDates(local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, local.prevTermSubID)>			
						<cfif (local.prevTermDatesTest.success eq true) AND 
									(len(local.prevTermDatesTest.subTermStartDate)) AND 
									(len(local.prevTermDatesTest.subTermEndDate)) AND
									(local.prevTermDatesTest.subTermStatusCode neq 'E')>
									
							<cfset local.dateStartComp = dateCompare(local.newStartDate, local.prevTermDates.subTermEndDate)>
							<cfset local.dateEndComp = dateCompare(local.newEndDate, local.prevTermDates.subTermStartDate)>
							<cfif ((local.dateStartComp eq -1) OR (local.dateStartComp eq 0)) AND
										((local.dateEndComp eq 1) OR (local.dateEndComp eq 0))>
								<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=changeDates&puid=#local.puid#&derr=1" addtoken="no">
							</cfif>
						</cfif>
						
						<cfset local.subNode = xmlSearch(local.xmlSubscribeMember,"//set[@id='0']/subscription")>
						<cfset local.subNode[1].xmlAttributes.startDateOverride = local.newStartDate>
						<cfset local.subNode[1].xmlAttributes.endDateOverride = local.newEndDate>
						<cfset local.subNode[1].xmlAttributes.graceEndDateOverride = local.newGraceEndDate>
					</cfif>

					<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
						<cfset local.arrSubs = xmlsearch(local.xmlSubscribeMember,"//set/subscription")>
						<cfif arrayLen(local.arrSubs)>
							<cfloop array="#local.arrSubs#" index="local.thisSubRow">
								<cfset local.tmpUIDNoDash = replace(local.thisSubRow.xmlAttributes.uid,"-","","ALL")>
								<cfif (local.thisSubRow.xmlAttributes.currstatus eq 'D') OR (StructKeyExists(local.thisSubRow.xmlAttributes,"deleteme") AND local.thisSubRow.xmlAttributes.deleteme eq 1)>
								<cfelseif len(arguments.event.getValue("fRecogFrom#local.tmpUIDNoDash#","")) and len(arguments.event.getValue("fRecogTo#local.tmpUIDNoDash#",""))>
									<cfset local.thisSubRow.xmlAttributes.recogStartDate = arguments.event.getValue("fRecogFrom#local.tmpUIDNoDash#")>
									<cfset local.thisSubRow.xmlAttributes.recogEndDate = arguments.event.getValue("fRecogTo#local.tmpUIDNoDash#")>
								</cfif>
							</cfloop>
						</cfif>
					</cfif>

					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.event.getValue('mid'), subXML=toString(local.xmlSubscribeMember))>

					<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.puid#" addtoken="no">
				</cfcase>

				<cfcase value="showConfirm">
					<cfset local.memberID = local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid>

					<cfset local.subStartOverrideDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
					<cfset local.subEndOverrideDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>
					<cfset local.dateNow = DateFormat(now(), "mm/dd/yyyy")>
					<cfset local.acceptDate = ''>
					<cfif (len(local.subStartOverrideDate) neq 0) OR (len(local.subEndOverrideDate) neq 0)>
						<cfset local.subStartCompare = DateCompare(local.subStartOverrideDate, local.dateNow)>
						<cfset local.subEndCompare = DateCompare(local.subEndOverrideDate, local.dateNow)>
						
						<cfif local.subEndCompare lte 0>
							<cfset local.acceptDate = local.subStartOverrideDate>
						<cfelse>
							<cfset local.acceptDate = ''>
						</cfif>
					<cfelse>
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
							select subStartDate, subEndDate, graceEndDate
							from dbo.sub_subscribers
							where subscriberID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@sid)")#">
						</cfquery>
						
						<cfset local.subStartDate = DateFormat(local.qrySubDates.subStartDate, "mm/dd/yyyy")>
						<cfset local.subEndDate = DateFormat(local.qrySubDates.subEndDate, "mm/dd/yyyy")>
						<cfset local.subGraceEndDate = local.qrySubDates.graceEndDate>
						<cfif len(local.subGraceEndDate) gt 0>
							<cfset local.subGraceEndDate = DateFormat(local.subGraceEndDate, "mm/dd/yyyy")>
						</cfif>

						<cfset local.subStartCompare = DateCompare(DateFormat(local.qrySubDates.subStartDate, "mm/dd/yyyy"), local.dateNow)>
						<cfset local.subEndCompare = DateCompare(DateFormat(local.qrySubDates.subEndDate, "mm/dd/yyyy"), local.dateNow)>
						
						<cfif local.subEndCompare lte 0>
							<cfset local.acceptDate = DateFormat(local.qrySubDates.subStartDate, "mm/dd/yyyy")>
						<cfelse>
							<cfset local.acceptDate = ''>
						</cfif>
						
					</cfif>				

					<cfset local.arrPaySchedule = arrayNew(1)>
					<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
						<cfset local.strPS = { date='', amount='' }>
						<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
					</cfloop>
					<cfset local.chkRates = checkRates(subXML=local.xmlSubscribeMember)>
		
					<cfset local.initSpread = false>
					<cfset local.spreadPayOrder = false>
					<cfset local.amountToCharge = 0>
					<cfset local.firstPaymentMinimum = 0>
					<cfset local.numPaymentsToUse = 0>

					<cfset local.sidList = ''>
					<cfloop array="#XMLSearch(local.xmlSubscribeMember,'//subscription/@sid')#" index="local.thisSID">
						<cfif (Len(local.thisSID.xmlValue) gt 0) AND (val(local.thisSID.xmlValue) neq 0)>
							<cfset local.sidList = ListAppend(local.sidList, val(local.thisSID.xmlValue))>
						</cfif>
					</cfloop>
					
					<!--- the following query is used in the javascript for the displaying page --->
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoicedSubs">
						SET NOCOUNT ON;

						declare @orgID int;

						IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
							DROP TABLE ##mcSubscribersForAcct;
						IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
							DROP TABLE ##mcSubscriberTransactions;

						CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
						CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
							invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
							amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
							assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

						INSERT INTO ##mcSubscribersForAcct (subscriberID)
						select li.listItem
						from dbo.fn_IntListToTable(<cfqueryparam value="#local.sidList#" cfsqltype="cf_sql_varchar">,',') as li;

						select top 1 @orgID = s.orgID
						from dbo.sub_subscribers as s
						inner join ##mcSubscribersForAcct as tmp on tmp.subscriberID = s.subscriberID;

						EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

						select sum(st.amount) as totalInvoicedRemaining
						from dbo.sub_subscribers s
						inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
						inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
						where s.orgID = @orgID
						and s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
						and ins.status IN ('Closed','Delinquent');

						IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
							DROP TABLE ##mcSubscribersForAcct;
						IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
							DROP TABLE ##mcSubscriberTransactions;
					</cfquery>

					<cfset local.subInvoiceCount = xmlSearch(local.xmlSubscribeMember,"count(//invoice)")>
					<cfif local.subInvoiceCount gt 0>

						<!--- create the initial pay structure --->
						<cfset local.currLoopDate = ''>
						<cfset local.arrLoopCnt = 0>
						<cfloop array="#xmlSearch(local.xmlSubscribeMember,'//invoice')#" index="local.thisInvoice">
							<cfif (len(local.currLoopDate) eq 0) OR (local.currLoopDate neq local.thisInvoice.xmlAttributes.dateDue)>
								<cfset local.arrLoopCnt = local.arrLoopCnt + 1>
								<cfset local.arrPaySchedule[local.arrLoopCnt].date = local.thisInvoice.xmlAttributes.dateDue>
								<cfset local.arrPaySchedule[local.arrLoopCnt].profiles = StructNew()>
								<cfset local.currLoopDate = local.thisInvoice.xmlAttributes.dateDue>
								<cfif NOT structKeyExists(local.arrPaySchedule[local.arrLoopCnt],"amount")>
									<cfset local.arrPaySchedule[local.arrLoopCnt].amount = 0>
								</cfif>
							</cfif>

							<cfset local.arrPaySchedule[local.arrLoopCnt].amount = numberFormat(val(local.arrPaySchedule[local.arrLoopCnt].amount) + local.thisInvoice.xmlAttributes.sumtotal, "_.__")>
							<cfif StructKeyExists(local.arrPaySchedule[local.arrLoopCnt], "prevAmt")>
								<cfset local.arrPaySchedule[local.arrLoopCnt].prevAmt = numberFormat(val(local.arrPaySchedule[local.arrLoopCnt].prevAmt) + local.thisInvoice.xmlAttributes.sumTotal, "_.__")>
							<cfelse>
								<cfset local.arrPaySchedule[local.arrLoopCnt].prevAmt = numberFormat(local.thisInvoice.xmlAttributes.sumTotal, "_.__")>
							</cfif>
							<cfset local.arrPaySchedule[local.arrLoopCnt].profiles["#local.thisInvoice.xmlAttributes.invoiceprofileid#"] = {invoiceID=local.thisInvoice.xmlAttributes.id, date=local.thisInvoice.xmlAttributes.dateDue, amount=numberFormat(local.thisInvoice.xmlAttributes.sumtotal, "_.__"), prevAmt=numberFormat(local.thisInvoice.xmlAttributes.sumTotal, "_.__") }>

							<cfset local.amountToCharge = local.amountToCharge + local.thisInvoice.xmlAttributes.sumtotal>
						</cfloop>

						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOrigPrice">
							SET NOCOUNT ON;

							declare @orgID int;

							IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
								DROP TABLE ##mcSubscribersForAcct;
							IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
								DROP TABLE ##mcSubscriberTransactions;

							CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
							CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
								invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
								amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
								assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
								creditGLAccountID int);

							INSERT INTO ##mcSubscribersForAcct (subscriberID)
							select li.listItem
							from dbo.fn_IntListToTable(<cfqueryparam value="#local.sidList#" cfsqltype="cf_sql_varchar">,',') as li;

							select top 1 @orgID = s.orgID
							from dbo.sub_subscribers as s
							inner join ##mcSubscribersForAcct as tmp on tmp.subscriberID = s.subscriberID;

							EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

							select s.subscriberID, s.subscriptionID, isNull(r.forceUpfront, 0) as forceUpfront, st.invoiceID, st.invoiceProfileID, sum(st.amount) as totalRemaining
							from dbo.sub_subscribers s
							inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
							left outer join dbo.sub_rateFrequencies as rf on rf.rfid = s.rfid
							left outer join dbo.sub_rates r on r.rateID = rf.rateID
							inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
							where s.orgID = @orgID
							and s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
							and ins.status = 'Pending'
							group by s.subscriberID, s.subscriptionID, isNull(r.forceUpfront, 0), st.invoiceID, st.invoiceProfileID;

							IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
								DROP TABLE ##mcSubscribersForAcct;
							IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
								DROP TABLE ##mcSubscriberTransactions;
						</cfquery>
						
						<!--- match the new items to the existing pay structure --->
						<cfloop array="#XMLSearch(local.xmlSubscribeMember,'//subscription')#" index="local.thisSub">

							<cfif local.numPaymentsToUse eq 0>
								<cfset local.numPaymentsToUse = local.thisSub.xmlAttributes.rateInstallments>
							</cfif>
							<cfif (local.thisSub.xmlAttributes.alreadysub neq true) 
										OR (local.thisSub.xmlAttributes.currstatus eq 'R') 
										OR (local.thisSub.xmlAttributes.currstatus eq 'O')
										OR (((local.thisSub.xmlAttributes.alreadysub eq true) AND (local.thisSub.xmlAttributes.hasinvoiceid neq 'true'))
										AND (StructKeyExists(local.thisSub.xmlAttributes, "readdme") AND (local.thisSub.xmlAttributes.readdme eq 1))
										AND (StructKeyExists(local.thisSub.xmlAttributes, "deleteme") AND (local.thisSub.xmlAttributes.deleteme neq 1)))>
										
								<cfif local.thisSub.xmlAttributes.pcisfree eq "false">
									<cfset local.spreadPayOrder = true>
									<cfset local.loopRateToUse = getRateToUse(useRates=local.thisSub.xmlAttributes.userates, 
																														rateAmt=local.thisSub.xmlAttributes.rateAmt, 
																														rateInstallments=local.thisSub.xmlAttributes.rateInstallments, 
																														numPaymentsToUse=local.numPaymentsToUse,
																														pcPctOff=local.thisSub.xmlAttributes.pcpctoff, 
																														pcRateAmt=local.thisSub.xmlAttributes.pcrateamt)>

									<cfif local.thisSub.xmlAttributes.forceUpfront eq 1>
										<cfset local.loopAmt = 0>
										<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[1].amount)>
										<cfset local.arrPaySchedule[1].amount = numberformat((local.loopAmt + val(local.loopRateToUse.rateTotal)), "_.__")>
										
										<cfset local.firstPaymentMinimum = numberformat(local.firstPaymentMinimum + val(local.loopRateToUse.rateTotal), "_.__")>
										<cfset local.amountToCharge = local.amountToCharge + local.loopRateToUse.rateTotal>

									<cfelse>
										<cfif local.thisSub.xmlAttributes.userates is 1>
											<!--- use local.loopRateToUse.rateTotal --->
											<cfset local.rateToUse = local.loopRateToUse.rateTotal / local.arrLoopCnt>
											<cfset local.rateLeftover = local.loopRateToUse.rateTotal - (numberformat((local.loopRateToUse.rateTotal / local.arrLoopCnt), "_.__") * local.arrLoopCnt)>
		
											<cfloop from="1" to="#local.arrLoopCnt#" index="local.loopCnt">
												<cfset local.loopAmt = 0>
												<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
												
												<cfset local.arrPaySchedule[local.loopCnt].amount = numberformat(local.loopAmt + val(local.rateToUse), "_.__")>
											</cfloop>
						
											<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.rateLeftover), "_.__") >
						
											<cfset local.amountToCharge = local.amountToCharge + local.loopRateToUse.rateTotal>
											
										<cfelse>
											
											<cfset local.rateToUse = local.loopRateToUse.rateTotal / local.arrLoopCnt>
											<cfset local.rateLeftover = local.loopRateToUse.rateTotal - (numberformat((local.loopRateToUse.rateTotal / local.arrLoopCnt), "_.__") * local.arrLoopCnt)>
		
											<cfloop from="1" to="#local.arrLoopCnt#" index="local.loopCnt">
												<cfset local.loopAmt = 0>
												<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
												
												<cfset local.arrPaySchedule[local.loopCnt].amount = numberformat(local.loopAmt + val(local.rateToUse), "_.__")>
											</cfloop>
						
											<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.rateLeftover), "_.__") >

											<cfset local.amountToCharge = local.amountToCharge + local.loopRateToUse.rateTotal>

										</cfif>
									</cfif>
								</cfif>
							<cfelseif (local.thisSub.xmlAttributes.alreadysub eq true) AND 
												(StructKeyExists(local.thisSub.xmlAttributes, "deleteme") AND (local.thisSub.xmlAttributes.deleteme eq 1))>

								<cfquery dbtype="query" name="local.qrySubInvInfo">
									select invoiceID, invoiceProfileID, totalRemaining
									from [local].qryOrigPrice
									where subscriberID = #local.thisSub.xmlAttributes.sid#
								</cfquery>
								
								<cfloop from="1" to="#local.arrLoopCnt#" index="local.loopCnt">
									<cfloop query="local.qrySubInvInfo">
										<cfif StructKeyExists(local.arrPaySchedule[local.loopCnt], "profiles") AND
													StructKeyExists(local.arrPaySchedule[local.loopCnt].profiles, local.qrySubInvInfo.invoiceProfileID) AND
													(local.arrPaySchedule[local.loopCnt].profiles["#local.qrySubInvInfo.invoiceProfileID#"].invoiceID eq local.qrySubInvInfo.invoiceID)>
		
											<!--- take out the old --->				
											<cfset local.arrPaySchedule[local.loopCnt].amount = numberformat(local.arrPaySchedule[local.loopCnt].amount - val(local.qrySubInvInfo.totalRemaining), "_.__")>
											<cfset local.arrPaySchedule[local.loopCnt].profiles["#local.qrySubInvInfo.invoiceProfileID#"].amount = numberformat(local.arrPaySchedule[local.loopCnt].profiles["#local.qrySubInvInfo.invoiceProfileID#"].amount - val(local.qrySubInvInfo.totalRemaining), "_.__")>
											<cfset local.amountToCharge = local.amountToCharge - val(local.qrySubInvInfo.totalRemaining)>
										
										
										
										</cfif>
									</cfloop>
								</cfloop>
							<cfelseif (local.thisSub.xmlAttributes.alreadysub eq true) AND (local.thisSub.xmlAttributes.pricechanged eq 1)>

								<cfset local.spreadPayOrder = true>
								<!--- need to find out what parts of what invoices it is, then replace those amounts with the new amount --->
								<cfquery dbtype="query" name="local.qrySubInvInfo">
									select invoiceID, invoiceProfileID, totalRemaining
									from [local].qryOrigPrice
									where subscriberID = #local.thisSub.xmlAttributes.sid#
								</cfquery>
								
								<cfset local.rateToUse = numberformat(local.thisSub.xmlAttributes.pcrateamt / local.arrLoopCnt, "_.__")>
								<cfset local.rateLeftover = local.thisSub.xmlAttributes.pcrateamt - (numberformat((local.thisSub.xmlAttributes.pcrateamt / local.arrLoopCnt), "_.__") * local.arrLoopCnt)>
								
								<cfloop from="1" to="#local.arrLoopCnt#" index="local.loopCnt">
									<cfloop query="local.qrySubInvInfo">
										<cfif StructKeyExists(local.arrPaySchedule[local.loopCnt], "profiles") AND
													StructKeyExists(local.arrPaySchedule[local.loopCnt].profiles, local.qrySubInvInfo.invoiceProfileID) AND
													(local.arrPaySchedule[local.loopCnt].profiles["#local.qrySubInvInfo.invoiceProfileID#"].invoiceID eq local.qrySubInvInfo.invoiceID)>
		
											<!--- take out the old --->				
											<cfset local.arrPaySchedule[local.loopCnt].amount = numberformat(local.arrPaySchedule[local.loopCnt].amount - val(local.qrySubInvInfo.totalRemaining), "_.__")>
											<cfset local.amountToCharge = local.amountToCharge - val(local.qrySubInvInfo.totalRemaining)>
										
										</cfif>
									</cfloop>
									
									<cfif local.thisSub.xmlAttributes.forceUpfront neq 1>
										<!--- add the new --->
										<cfset local.arrPaySchedule[local.loopCnt].amount = numberformat(local.arrPaySchedule[local.loopCnt].amount + val(local.rateToUse), "_.__")>
										<cfset local.amountToCharge = local.amountToCharge + val(local.rateToUse)>
									</cfif>
								</cfloop>
								
								<cfif local.thisSub.xmlAttributes.forceUpfront neq 1>
									<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.rateLeftover), "_.__") >
									<cfset local.amountToCharge = local.amountToCharge + val(local.rateLeftover)>
								<cfelse>
									<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.thisSub.xmlAttributes.pcrateamt), "_.__") >
									<cfset local.amountToCharge = local.amountToCharge + val(local.thisSub.xmlAttributes.pcrateamt)>
									<cfset local.firstPaymentMinimum = numberformat(local.firstPaymentMinimum + val(local.thisSub.xmlAttributes.pcrateamt), "_.__")>
								</cfif>

							<cfelseif (local.thisSub.xmlAttributes.alreadysub eq true) AND (local.thisSub.xmlAttributes.forceUpfront eq 1)>

								<cfset local.spreadPayOrder = true>

								<cfquery dbtype="query" name="local.qrySubInvInfo">
									select invoiceID, invoiceProfileID, totalRemaining
									from [local].qryOrigPrice
									where subscriberID = #local.thisSub.xmlAttributes.sid#
								</cfquery>

								<cfset local.loopAmt = 0>
								<cfloop from="1" to="#local.arrLoopCnt#" index="local.loopCnt">
									<cfloop query="local.qrySubInvInfo">
										<cfif StructKeyExists(local.arrPaySchedule[local.loopCnt], "profiles") AND
													StructKeyExists(local.arrPaySchedule[local.loopCnt].profiles, local.qrySubInvInfo.invoiceProfileID) AND
													(local.arrPaySchedule[local.loopCnt].profiles["#local.qrySubInvInfo.invoiceProfileID#"].invoiceID eq local.qrySubInvInfo.invoiceID)>
		
											<!--- take out the old --->				
											<cfset local.arrPaySchedule[local.loopCnt].amount = numberformat(local.arrPaySchedule[local.loopCnt].amount - val(local.qrySubInvInfo.totalRemaining), "_.__")>
											<cfset local.amountToCharge = local.amountToCharge - val(local.qrySubInvInfo.totalRemaining)>
										
											<cfset local.loopAmt = local.loopAmt + val(local.qrySubInvInfo.totalRemaining)>
										</cfif>
									</cfloop>
								</cfloop>

								<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.loopAmt), "_.__") >
								<cfset local.firstPaymentMinimum = numberformat(local.firstPaymentMinimum + val(local.loopAmt), "_.__")>
								
								<cfset local.amountToCharge = local.amountToCharge + val(local.loopAmt)>
							
							</cfif>

						</cfloop>

						<cfset local.subAmts = getSubscriptionAmounts(subXML=local.xmlSubscribeMember, numPaymentsToUse=local.numPaymentsToUse)>
						<cfset local.distribAmt = local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse>
						<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.subAmts.qryNonUpFrontAmt.totalAmt - (numberformat((local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

						<!--- tests --->
						<cfset local.testStruct = StructNew()>
						<cfset local.testStruct.arrPaySchedule = local.arrPaySchedule>
						<cfset local.testStruct.calcAmounts = StructNew()>
						<cfset local.testStruct.calcAmounts.amountToCharge = local.amountToCharge>
						<cfset local.testStruct.calcAmounts.firstPaymentMinimum = local.firstPaymentMinimum>
						
						<cfset local.testStruct.subAmts = local.subAmts>
						
						<cfset local.testHasError = false>
						<cfset local.testErrMessage = ''>

						<!--- compare, throw error if not the same --->
						<cfif numberformat(local.amountToCharge, "_.__") neq numberformat(local.subAmts.qryTotals.totalAmt, "_.__")>
							<cfset local.testHasError = true>
							<cfset local.testErrMessage = local.testErrMessage & 'Total does not match.<br/>'>
						<cfelseif numberformat(local.firstPaymentMinimum, "_.__") neq numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__")>
							<cfset local.testHasError = true>
							<cfset local.testErrMessage = local.testErrMessage & 'First Minimum Payment does not match.<br/>'>
						</cfif>
						
						<cfif local.testHasError>
							<cftry>
								<cfthrow message="#local.testErrMessage#">
							<cfcatch type="any">
								<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.testStruct)>
							</cfcatch>	
							</cftry>

							<cfthrow detail="#local.testErrMessage#">
						</cfif>
						
					<cfelse>
						<cfset local.termDateRFID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rfid)")>
						<cfset local.subTermFlag = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@termflag)")>
						<cfset local.rootNumPayments = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rateInstallments)")>
						<cfset local.rootRateInterval = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@rateInterval)")>
						<cfset local.rootFreqID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@freqid)")>
						<cfif (len(local.subStartOverrideDate) neq 0) OR (len(local.subEndOverrideDate) neq 0)>
							<cfset local.subPaymentDates = getSubPaymentDates(local.subStartOverrideDate,
																																local.subEndOverrideDate,
																																local.rootNumPayments,
																																local.rootRateInterval)>
						<cfelseif structKeyExists(local,"subStartDate") and structKeyExists(local,"subEndDate") and isDate(local.subStartDate) and isDate(local.subEndDate)>
							<cfset local.subPaymentDates = getSubPaymentDates(local.subStartDate,
									local.subEndDate,
									local.rootNumPayments,
									local.rootRateInterval)>

						<cfelse>
							<cfset local.subTermDates = getSubTermDates(termDateRFID=local.termDateRFID, subTermFlag=local.subTermFlag)>
							<cfset local.subPaymentDates = getSubPaymentDates(local.subTermDates.subTermStartDate,
																																local.subTermDates.subTermEndDate,
																																local.rootNumPayments,
																																local.rootRateInterval)>
						</cfif>
						
						<cfset local.numPaymentsToUse = local.subPaymentDates.numPayments>

						<cfset local.subAmts = getSubscriptionAmounts(subXML=local.xmlSubscribeMember, numPaymentsToUse=local.numPaymentsToUse)>
		
						<cfset local.amountToCharge = local.subAmts.qryTotals.totalAmt>
						
						<cfif local.subAmts.qryUpFrontAmt.totalAmt gt 0>
							<cfset local.arrPaySchedule[1] = { date=now(), amount=numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__") }>
							<cfset local.firstPaymentMinimum = numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__")>
						</cfif>
		
						<cfset local.distribAmt = local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse>
						<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.subAmts.qryNonUpFrontAmt.totalAmt - (numberformat((local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

						
						<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
							<cfset local.loopAmt = 0>
							<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
							<cfset local.arrPaySchedule[local.loopCnt] = { date=now(), amount=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
						</cfloop>
						<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >


						<cfloop from="1" to="#ArrayLen(local.subPaymentDates.arrDates)#" index="local.thisP">
							<cfset local.arrPaySchedule[local.thisP].date = local.subPaymentDates.arrDates[local.thisP]>
						</cfloop>
						
						<cfset local.initSpread = true>
						<cfset local.numPaymentsToSpread = local.numPaymentsToUse>
					</cfif>

					<cfset local.currTopStatus = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@currstatus)")>

					<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.memberID, subXML=toString(local.xmlSubscribeMember))>

					<cfset local.dspStep = "confirm">
				</cfcase>

				<cfcase value="doConfirm">
					<cfset local.retStruct = doConfirm(subXML=local.xmlSubscribeMember, strEventCollection=arguments.event.getCollection())>
					<cfif local.retStruct.success eq 0>
						<cfset local.errMsg = "We were unable to update this subscription.">
						<cftry>
							<cfthrow message="Unable to update subscription.">
						<cfcatch type="any">
							<cfset local.tmpErrStr = { errorSection=local.retStruct.errorSection, sql=replace(local.retStruct.sqlTemp,chr(10),"<br/>","ALL") }>
							<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.tmpErrStr)>
						</cfcatch>	
						</cftry>
						
						<cfset local.dspStep = "confirmError">
					<cfelse>
						<cfset local.dspStep = "confirmDone">
					</cfif>
				</cfcase>
				
				<!--- show initial subs --->
				<cfdefaultcase>
					<cfset local.reLoad = loadXMLForEdit(memberID=arguments.event.getValue('mid'), siteID=arguments.event.getValue('mc_siteinfo.siteid'), rootSubID=arguments.event.getValue('sid',0))>
					<cfset local.xmlSubscribeMember = local.reLoad.subXML>
					<cfset local.subTermStartDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
					<cfset local.subTermEndDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>

					<cfif (len(local.subTermStartDate) eq 0) OR (len(local.subTermEndDate) eq 0)>
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
							select subStartDate, subEndDate, graceEndDate
							from dbo.sub_subscribers
							where subscriberID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@sid)")#">
						</cfquery>
						
						<cfset local.subTermStartDate = DateFormat(local.qrySubDates.subStartDate, "mm/dd/yyyy")>
						<cfset local.subTermEndDate = DateFormat(local.qrySubDates.subEndDate, "mm/dd/yyyy")>
					</cfif>

					<cfif local.reLoad.success neq true>
						<cflocation url="#arguments.event.getValue('mainsuburl')#" addtoken="no">
					<cfelse>
						
						<!--- 
						initial editing of a sub should check for any pending invoices that are due earlier than any closed/delinq/paid invoices tied to this subscription.
						If we find any, close them.
						--->
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryClosePendingInvoices">
							SET NOCOUNT ON;
							SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

							declare @maxClosedInvoiceDueDate datetime, @invoiceStatusID_closed int, @invoiceStatusID_pending int,
								@invoiceStatusID_delinq int, @invoiceStatusID_paid int, @invoiceIDList varchar(max), @currentMemberID int, 
								@rootSubscriberID int, @orgID int;
							declare @tblInvoices TABLE (invoiceID int, statusID int, dateDue date);

							set @currentMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
							set @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid')#">;
							set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

							select @invoiceStatusID_pending = statusID from dbo.tr_invoiceStatuses where status = 'Pending';
							select @invoiceStatusID_closed = statusID from dbo.tr_invoiceStatuses where status = 'Closed';
							select @invoiceStatusID_delinq = statusID from dbo.tr_invoiceStatuses where status = 'Delinquent';
							select @invoiceStatusID_paid = statusID from dbo.tr_invoiceStatuses where status = 'Paid';

							insert into @tblInvoices (invoiceID, statusID, dateDue)
							select i.invoiceID, i.statusID, i.dateDue
							from dbo.sub_subscribers ss
							inner join dbo.tr_applications ta on ta.orgID = @orgID and ss.subscriberID = ta.itemID
								and ta.itemType = 'Dues'
								and ss.rootSubscriberID = @rootSubscriberID
							inner join dbo.tr_invoiceTransactions it on it.orgID = @orgID and it.transactionID = ta.transactionID
							inner join dbo.tr_invoices i on i.orgID = @orgID and i.invoiceID = it.invoiceID;

							select @maxClosedInvoiceDueDate = max(dateDue)
							from @tblInvoices
							where statusID in (@invoiceStatusID_closed,@invoiceStatusID_delinq,@invoiceStatusID_paid);

							select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(10)) 
							from @tblInvoices
							where statusID = @invoiceStatusID_pending
							and dateDue <= @maxClosedInvoiceDueDate;

							SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

							IF @invoiceIDList is not null
								EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@currentMemberID, @invoiceIDList=@invoiceIDList;
						</cfquery>

						<cfset local.subUID = local.reLoad.subUID>
						<cflocation url="#arguments.event.getValue('mainsuburl')#&subAction=showAddons&puid=#local.subUID#" addtoken="no">
					</cfif>
				</cfdefaultcase>
			</cfswitch>

			<!--- get member info --->
			<cfset local.qryMember = CreateObject("component","model.admin.members.members").getMember_demo(memberid=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid)>
			<cfset local.canAccept = CreateObject("component","subscriptions").validateSubscriptionStructureXML(xmlSubscribeMember=local.xmlSubscribeMember).isValid>

			<!--- no member found --->
			<cfif val(local.qryMember.memberid) is 0> 
				<cfsavecontent variable="local.data">
					<cfoutput>That member could not be found.</cfoutput>
				</cfsavecontent>

			<!--- good member --->
			<cfelse>
				<cfset local.subTreeVerbose = getSubTreeVerbose(subXML=local.xmlSubscribeMember, siteID=arguments.event.getValue('mc_siteinfo.siteid'), termDateString=local.termDateString, useAccrualAccounting = arguments.event.getValue('mc_siteInfo.useAccrualAcct'))>
				
				<cfif arguments.event.getValue('subAction') eq "showConfirm">
					<cfset local.qryTaxStateZIP = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberid=local.qryMember.memberid)>
				</cfif>
				
				<cfsavecontent variable="local.data">
					<cfinclude template="dsp_subscribeEdit.cfm">
				</cfsavecontent>			
			</cfif>
		<cfcatch type="any">
			<cfset local.arguments = arguments />
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="exception in model.admin.subscriptions.subscriptionReg.doSubscribeEdit + rethrow")>
			<cfrethrow>
		</cfcatch>
		</cftry>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAddOnsWithSubs" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="isRenewal" type="boolean" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery name="local.qryAddOnsWithSubs" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			
			declare @FID int, @memberid int, @siteID int, @subscriptionID int, @rootSubscriberID int;
			select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			select @siteid = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">;
			select @memberid = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">;
			select @subscriptionID = <cfqueryparam value="#arguments.subscriptionID#" cfsqltype="CF_SQL_INTEGER">;
			select @rootSubscriberID = <cfqueryparam value="#arguments.rootSubscriberID#" cfsqltype="CF_SQL_INTEGER">;
			
			select ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.allowRateGLAccountOverride,s.scheduleID,s.uid,
							o.subActivationCode, o2.subActivationCode as subAlternateActivationCode, sets.setid, sets.setName, 
							ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, convert(integer, ao.frontEndAllowSelect) as frontEndAllowSelect, convert(integer, ao.frontEndAddAdditional) as frontEndAddAdditional, convert(integer, ao.frontEndAllowChangePrice) as frontEndAllowChangePrice,
							s.paymentOrder as subPayOrder, ao.childSetID, ao.orderNum, max(sr.subscriberID) as existingSubscriberID
			from dbo.sub_addons as ao
			inner join dbo.sub_sets as sets on sets.setID = ao.childSetID
			INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
			INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID and s.status = 'A'
			INNER JOIN dbo.sub_activationOptions as o on o.subActivationID = s.subActivationID
			INNER JOIN dbo.sub_activationOptions as o2 on o2.subActivationID = s.subAlternateActivationID
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
			inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' <cfif arguments.isRenewal>and r.isRenewalRate = 1 <cfelse>and r.isRenewalRate = 0 </cfif>
				and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteID
				and srfrp.siteResourceID = r.siteResourceID
			    AND srfrp.functionID = @FID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
				and srfrp.rightPrintID = gprp.rightPrintID
			inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
			    and m.memberID = @memberID						
			left outer join dbo.sub_subscribers sr 
				inner join dbo.sub_statuses st on st.statusID = sr.statusID and st.canEdit = 1
				on sr.subscriptionID = s.subscriptionID and sr.memberID = @memberID and sr.rootSubscriberID = @rootSubscriberID
			where ao.subscriptionID = @subscriptionID
			group by ao.orderNum, ss.orderNum, ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID,
				s.allowRateGLAccountOverride, s.scheduleID, s.uid, o.subActivationCode, o2.subActivationCode, sets.setid, sets.setName,
				ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice,
				s.paymentOrder, ao.childSetID
			order by ao.orderNum, ss.orderNum;
		</cfquery>

		<cfreturn local.qryAddOnsWithSubs>
	</cffunction>

	<cffunction name="loadXMLForEdit" access="private" output="no" returntype="struct">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="rootSubID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.reStruct = structNew()>
		<cfset local.reStruct.success = true>
		
		<cfset local.xmlSubscribeMember = resetSubXML(memberID=arguments.memberID)>
		<cfset local.puid = xmlSearch(local.xmlSubscribeMember,"string(//process/@pointer)")>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriber">
			set nocount on;
			
			declare @subscriptionID int, @rootsubscriberID int, @memberID int,  @functionID int,  @currentDate datetime, @siteResourceStatusID int, 
				@useRenewalRates bit, @siteID int;
			set @currentDate = getdate();
			set @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			set @rootsubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubID#">;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			select @subscriptionID = subscriptionID, @memberID = m.activememberID,
				@useRenewalRates = case when st.statusCode in ('R','O') then r.isRenewalRate else 0 end
			from dbo.sub_subscribers ss
			inner join dbo.ams_members m on m.memberID =ss.memberID
				and subscriberID = @rootsubscriberID
			inner join dbo.sub_statuses st on st.statusID = ss.statusID
			inner join dbo.sub_rateFrequencies rf on rf.rfid = ss.rfid
			inner join dbo.sub_rates r on r.rateID = rf.rateID;

			declare @subscriberQualifiedRateCount TABLE (subscriberID int, numQualifiedRates int);

			insert into @subscriberQualifiedRateCount (subscriberID, numQualifiedRates)
			select ss.subscriberID, count(distinct r.rateID) as numQualifiedRates
			from dbo.sub_subscribers ss
			inner join dbo.sub_rateFrequencies rf on rf.rfid = ss.rfid
				and ss.rootSubscriberID = @rootSubscriberID
			inner join dbo.sub_rates currentRate on currentRate.rateID = rf.rateID
			inner join dbo.sub_rates r on r.isRenewalRate = @useRenewalRates
				and r.scheduleID = currentRate.scheduleID
				and @currentDate between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
			inner join dbo.sub_rateFrequencies rfchoices on rfchoices.rateID = r.rateID
				and (rfchoices.allowFrontEnd = 1 or rfchoices.rfid = ss.rfid)
			inner join dbo.cms_siteResources sr on sr.siteID = @siteID
				and sr.siteResourceID = r.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID 
				and srfrp.siteResourceID = sr.siteResourceID
				and srfrp.functionID = @functionID
			inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
				and gprp.rightPrintID = srfrp.rightPrintID
			inner join dbo.ams_members m on m.memberID = @memberID
				and m.groupPrintID = gprp.groupPrintID
			group by ss.subscriberID;

			select rms.subscriberID, rms.subscriptionID, rms.typeName, rms.subscriptionName, rms.status, rms.RFID, rms.rateTermDateFlag, rms.GLAccountID, rms.subActivationCode, 
				rms.allowRateGLAccountOverride, rms.subStartDate, rms.subEndDate, rms.graceEndDate, rms.recogStartDate, rms.recogEndDate, 
				rms.parentSubscriberID, case when rms.PCFree = 1 then 'true' else 'false' end as PCFree, 
				rms.modifiedRate, rms.lastPrice, rms.paymentOrder, rms.thePath, rms.thePathExpanded, 
				rms.linkedNonRenewalRateID,rms.fallbackRenewalRateID, rms.keepChangedPriceOnRenewal, rms.frontEndAllowChangePrice, rms.frontEndChangePriceMin, rms.frontEndChangePriceMax,
				treeOrder.subscriptionPath,
                numQualifiedRates = isnull(qrc.numQualifiedRates,0), r.isRenewalRate
			from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @rootsubscriberID) rms
			inner join dbo.fn_sub_getSubscriptionTreeOrder(@subscriptionID) treeOrder on treeOrder.subscriptionID = rms.subscriptionID
			inner join dbo.sub_rateFrequencies rf on rf.rfid = rms.rfid
			inner join dbo.sub_rates r on r.rateID = rf.rateID
			left outer join @subscriberQualifiedRateCount qrc on qrc.subscriberID = rms.subscriberID
			where rms.status <> 'D'
			order by subscriptionPath;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddonNames">
			SET NOCOUNT ON;

			declare @siteID int, @orgID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			select @orgID = orgID from dbo.sites where siteID = @siteID;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

			INSERT INTO ##mcSubscribersForAcct (subscriberID)
			select rms.subscriberID
			from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, @siteID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubID#">) rms;

			EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

			select rms.subscriberID, rms.subscriptionID, rms.typeName, rms.subscriptionName, rms.status, rms.RFID, rms.rateTermDateFlag, rms.GLAccountID,
				rms.subStartDate, rms.subEndDate, rms.graceEndDate, rms.parentSubscriberID, rms.subActivationCode, rms.allowRateGLAccountOverride,
				case when rms.PCFree = 1 then 'true' else 'false' end as PCFree, rms.modifiedRate, rms.paymentOrder as subPayOrder,
				rms.thePath, rms.thePathExpanded, rms.recogStartDate, rms.recogEndDate, rms.linkedNonRenewalRateID,rms.fallbackRenewalRateID, 
				rms.keepChangedPriceOnRenewal, rms.frontEndAllowChangePrice, rms.frontEndChangePriceMin, rms.frontEndChangePriceMax,
				s.subscriptionID as parentSubscriptionID, st.invoiceID
			from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">, @siteID, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubID#">) rms
			left outer join dbo.sub_subscribers s on s.subscriberID = rms.parentSubscriberID
			left outer join ##mcSubscriberTransactions as st on st.subscriberID = rms.subscriberID
			where rms.status <> 'D'
			order by rms.thePath;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;
		</cfquery>

		<cfif local.qrySubscriber.recordcount eq 0>
			<cfset local.reStruct.success = false>
		<cfelse>

			<!--- Check top level sub (first row) to figure out if we should use renewal rates or not  --->
			<cfif ((local.qrySubscriber.status eq "R") OR (local.qrySubscriber.status eq "O")) AND (val(local.qrySubscriber.isRenewalRate) eq 1)>
				<cfset local.useRenewalRates = true>
			<cfelse>
				<cfset local.useRenewalRates = false>
			</cfif>
			<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes["useRenewalRates"] = local.useRenewalRates />

			<cfloop query="local.qrySubscriber">

				<cfquery dbtype="query" name="local.qryCheckInvoice">
					select invoiceID, parentSubscriptionID
					from [local].qryAddOnNames
					where subscriberID = #local.qrySubscriber.subscriberID#
				</cfquery>

				<cfif val(local.qrySubscriber.parentSubscriberID) eq 0>
					<cfset local.parentUID = local.puid>
				<cfelse>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddOnSet">
						select ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, sets.setid, sets.setName,
							ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice, s.paymentOrder as subPayOrder
						from dbo.sub_addons as ao
						inner join dbo.sub_sets as sets on sets.setID = ao.childSetID
						INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
						INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID
						INNER JOIN dbo.sub_activationOptions as o on o.subActivationID = s.subActivationID
						INNER JOIN dbo.sub_activationOptions as o2 on o2.subActivationID = s.subAlternateActivationID
						where s.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySubscriber.subscriptionID#">
						and ao.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCheckInvoice.parentSubscriptionID#">
					</cfquery>

					<cfset local.addonParentUID = xmlSearch(local.xmlSubscribeMember,"string(//subscription[@id=#local.qryCheckInvoice.parentSubscriptionID#]/@uid)")>

					<cfif local.qryAddOnSet.recordcount is 0>
						<!--- Need to handle this situation, addon assigned, but no longer in the same set or part of sub --->
						<cfset local.strAddSetResult = addSet(subXML=local.xmlSubscribeMember, parentUID=local.addonParentUID, setid=0, setname="Set Not Found", payOrder=0)>
					<cfelse>
	
						<cfset local.strAddSetResult = addSet(subXML=local.xmlSubscribeMember, parentUID=local.addonParentUID, setid=local.qryAddOnSet.setid, setname=local.qryAddOnSet.setname,
							feAllowSelect=local.qryAddOnSet.frontEndAllowSelect, feAddAdditional = local.qryAddOnSet.frontEndAddAdditional,
							feAllowChangePrice=local.qryAddOnSet.frontEndAllowChangePrice)>
					</cfif>

					<cfset local.parentUID = local.strAddSetResult.setUID>

					<cfif len(local.parentUID)>
						<cfset local.xmlSubscribeMember = local.strAddSetResult.subXML>
					</cfif>
				</cfif>

				<cfif val(local.qryCheckInvoice.invoiceID) gt 0>
					<cfset local.addonHasInvoiceID = true>
				<cfelse>
					<cfset local.addonHasInvoiceID = false>
				</cfif>
				
				<cfif len(local.parentUID)>
					<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.parentUID, subid=local.qrySubscriber.subscriptionID, 
												subname=local.qrySubscriber.subscriptionName, subTermFlag=local.qrySubscriber.rateTermDateFlag,
												GLAccountIDToUse=local.qrySubscriber.GLAccountID, allowRateGLOverride=local.qrySubscriber.allowRateGLAccountOverride,
												alreadySub=true, pcfree=local.qrySubscriber.PCFree, currstatus=local.qrySubscriber.status, 
												payOrder=local.qrySubscriber.paymentOrder, activationOptionCode=local.qrySubscriber.subActivationCode,
												alternateActivationOptionCode=local.qrySubscriber.subActivationCode, subscriberID=local.qrySubscriber.subscriberID, 
												hasInvoiceID=local.addonHasInvoiceID, frontEndAllowChangePrice=local.qrySubscriber.frontEndAllowChangePrice,
												frontEndChangePriceMin=local.qrySubscriber.frontEndChangePriceMin, frontEndChangePriceMax=local.qrySubscriber.frontEndChangePriceMax,
												numQualifiedRates = val(local.qrySubscriber.numQualifiedRates))>
					<cfif len(local.strAddSubResult.subUID)>
						<cfset local.strAddRateResult = addRate(subXML=local.strAddSubResult.subXML, parentUID=local.strAddSubResult.subUID, rfid=val(local.qrySubscriber.RFID), 
														modifiedRate=local.qrySubscriber.modifiedRate, 
														lastPrice=local.qrySubscriber.lastPrice,
														origRFID=val(local.qrySubscriber.RFID),
														recogStartDate=local.qrySubscriber.recogStartDate,
														recogEndDate=local.qrySubscriber.recogEndDate)>

						<cfset local.xmlSubscribeMember = local.strAddRateResult.subXML>

						<cfif val(local.qrySubscriber.parentSubscriberID) eq 0>
							<cfset local.reStruct.subUID = local.strAddSubResult.subUID>
						<cfelse>
						</cfif>
					</cfif>
				</cfif>
			</cfloop>

			<!--- ensure pricechanged is 0 for all subs upon initial loading --->
			<cfloop array="#XMLSearch(local.xmlSubscribeMember,'//subscription')#" index="local.thisSNode">
				<cfif local.thisSNode.xmlAttributes.pricechanged EQ 1>
					<cfset local.thisSNode.xmlAttributes.origpricechanged = 1>
					<cfset local.thisSNode.xmlAttributes.pricechanged = 0>
				</cfif>	
			</cfloop>
				
			<!--- add previous invoices that are pending.  Need to do it here before any editing takes place --->
			<cfset local.sidList = ''>
			<cfloop array="#XMLSearch(local.xmlSubscribeMember,'//subscription/@sid')#" index="local.thisSID">
				<cfif (Len(local.thisSID.xmlValue) gt 0) AND (val(local.thisSID.xmlValue) neq 0)>
					<cfset local.sidList = ListAppend(local.sidList, val(local.thisSID.xmlValue))>
				</cfif>
			</cfloop>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingInvoices">
				SET NOCOUNT ON;

				declare @orgID int, @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;

				IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
					DROP TABLE ##mcSubscribersForAcct;
				IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
					DROP TABLE ##mcSubscriberTransactions;

				CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
				CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
					invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
					amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
					assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

				INSERT INTO ##mcSubscribersForAcct (subscriberID)
				select li.listItem
				from dbo.fn_IntListToTable(<cfqueryparam value="#local.sidList#" cfsqltype="cf_sql_varchar">,',') as li;

				select top 1 @orgID = s.orgID
				from dbo.sub_subscribers as s
				inner join ##mcSubscribersForAcct as tmp on tmp.subscriberID = s.subscriberID;

				EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

				select st.invoiceID, convert(varchar, st.dateDue, 101) as dateDue, st.invoiceProfileID, st.mainTransactionID, 
					s.subscriberID, s.subscriptionID, s.PCFree, st.amount as totalRemaining
				from dbo.sub_subscribers s
				inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
				inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = s.memberID
				inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
				where s.orgID = @orgID 
				and mActive.memberID = @memberID
				and ins.status = 'Pending';

				IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
					DROP TABLE ##mcSubscribersForAcct;
				IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
					DROP TABLE ##mcSubscriberTransactions;
			</cfquery>

			<cfquery dbtype="query" name="local.qryTotalByInvoice">
				select invoiceID, dateDue, invoiceProfileID, sum(totalRemaining) as sumTotal
				from [local].qryExistingInvoices
				group by invoiceID, dateDue, invoiceProfileID			
			</cfquery>
			
			<cfset local.maxInvoiceID = val(ListLast(ListSort(valueList(local.qryExistingInvoices.invoiceID), "numeric")))>
			
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPayProfileID">
				select MPProfileID, payProfileID, payProcessFee, processFeePercent
				from dbo.sub_subscribers
				where subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubID#">
			</cfquery>
			
			<cfif val(local.qryPayProfileID.payProfileID) gt 0>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes["profileID"] = val(local.qryPayProfileID.MPProfileID)>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes["payProfileID"] = val(local.qryPayProfileID.payProfileID)>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes["payProcessFee"] = val(local.qryPayProfileID.payProcessFee)>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes["processFeePercent"] = val(local.qryPayProfileID.processFeePercent)>
			</cfif>
			
			<cfif local.qryTotalByInvoice.recordcount gt 0>
				<cfloop query="local.qryTotalByInvoice">
					<cfset local.strAddInvoiceResult = addInvoice(subXML=local.xmlSubscribeMember, invoiceID=local.qryTotalByInvoice.invoiceID, dateDue=local.qryTotalByInvoice.dateDue, 
														invoiceProfileID=local.qryTotalByInvoice.invoiceProfileID, sumTotal=local.qryTotalByInvoice.sumTotal)>
					<cfset local.xmlSubscribeMember = local.strAddInvoiceResult.subXML>
				</cfloop>
			</cfif>
						
		</cfif>

		<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=local.xmlSubscribeMember.xmlRoot.process.xmlAttributes.memberid, subXML=toString(local.xmlSubscribeMember))>
		<cfset local.reStruct["subXML"] = local.xmlSubscribeMember>

		<cfreturn local.reStruct>
	</cffunction>

	<cffunction name="getRateQuery" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="isRoot" type="boolean" required="true">
		<cfargument name="subID" type="string" required="true">
		<cfargument name="currFreqID" type="numeric" required="true">
		<cfargument name="currRateID" type="numeric" required="true">
		<cfargument name="currRFID" type="numeric" required="true">
		<cfargument name="isRenewal" type="boolean" required="true">
		<cfargument name="autoSelectRateMatch" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		
		<cfquery name="local.qryRatesAndFreqs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			
			declare @FID int, @memberID int, @groupPrintID int, @siteID int, @currRateID int, @currRFID int;
			select @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			set @siteid = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;
			set @currRateID = <cfqueryparam value="#arguments.currRateID#" cfsqltype="CF_SQL_INTEGER">;
			set @currRFID = <cfqueryparam value="#arguments.currRFID#" cfsqltype="CF_SQL_INTEGER">;

			-- ensure active member is considered
			select @memberID = activeMemberID from dbo.ams_members where memberID = @memberID;
			select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @memberID;
			
			select <cfif arguments.autoSelectRateMatch>Top 1</cfif>
				rfid, rateAmt, numInstallments, allowFrontEnd, frequencyName, frequency, frequencyID, rateName, rateID, frontEndAllowChangePrice
			from (
				select rf.rfid, rf.rateAmt, rf.numInstallments, rf.allowFrontEnd, r.rateID, f.frequencyName, f.frequency, f.frequencyID, 
					r.rateName, count(rfmp.rfmpid) as rfmpidCount, r.frontEndAllowChangePrice
				from dbo.sub_subscriptions as s
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
				inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					<cfif arguments.isRenewal>
						and r.isRenewalRate = 1
					<cfelse>
						and r.isRenewalRate = 0
					</cfif>
					and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteID
					AND srfrp.siteResourceID = r.siteResourceID
				    AND srfrp.functionID = @FID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
					AND srfrp.rightPrintID = gprp.rightPrintID
					AND gprp.groupPrintID = @groupPrintID
				inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID 
					and rf.rateAmt >= 0 
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.siteID = @siteID
					and f.status = 'A'
				left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid 
					and rfmp.status = 'A' 
				where s.subscriptionID = <cfqueryparam value="#arguments.subID#" cfsqltype="CF_SQL_INTEGER">
				group by rf.rfid, rf.rateAmt, rf.numInstallments, rf.allowFrontEnd, r.rateID, f.frequencyName, f.frequency, f.frequencyID, 
					r.rateName, r.frontEndAllowChangePrice
			) x 
			where x.rfmpidCount > 0
			<cfif arguments.autoSelectRateMatch>
				and rateID = @currRateID
				order by case when rfid = @currRFID then 1 else 0 end desc, numInstallments, rateName
			<cfelse>
				order by numInstallments, rateName
			</cfif>;
		</cfquery>

		<cfif (arguments.isRoot)>
			<cfset local.qryRates = local.qryRatesAndFreqs>
		<cfelse>
			<cfquery dbtype="query" name="local.qryFreqRates">
				select rfid, rateAmt, numInstallments, allowFrontEnd, frequencyName, frequency, frequencyID, rateName, rateID, frontEndAllowChangePrice
				from [local].qryRatesAndFreqs
				where frequencyID = #val(arguments.currFreqID)#
				order by numInstallments, rateName
			</cfquery>

			<cfif local.qryFreqRates.recordCount gt 0>
				<cfset local.qryRates = local.qryFreqRates>
			<cfelse>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFullRate">
					select frequencyID
					from dbo.sub_frequencies
					where siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
					and frequencyShortName = 'F'
					and isSystemRate = 1
					and status='A'
				</cfquery>
		
				<cfquery dbtype="query" name="local.qryFullRates">
					select rfid, rateAmt, numInstallments, allowFrontEnd, frequencyName, frequency, frequencyID, rateName, rateID, frontEndAllowChangePrice
					from [local].qryRatesAndFreqs
					where frequencyID = #val(local.qryFullRate.frequencyID)#
					order by numInstallments, rateName
				</cfquery>
				<cfset local.qryRates = local.qryFullRates>
			</cfif>	
		</cfif>
		<cfreturn local.qryRates>
	</cffunction>

	<cffunction name="checkRates" access="public" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.hasRateCount = 0>
		<cfset local.returnStruct.hasNoRateCount = 0>

		<cfloop array="#XMLSearch(arguments.subXML,'//subscription')#" index="local.thisSub">
			<cfif (local.thisSub.xmlAttributes.userates eq 1)>
				<cfif local.thisSub.xmlAttributes.rfid eq 0>
					<cfset local.returnStruct.hasNoRateCount = local.returnStruct.hasNoRateCount + 1>
				<cfelse>
					<cfset local.returnStruct.hasRateCount = local.returnStruct.hasRateCount + 1>
				</cfif>
			<cfelse>
				<cfset local.returnStruct.hasRateCount = local.returnStruct.hasRateCount + 1>
			</cfif>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="updateRates" access="private" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="origFreqID" type="numeric" required="true">
		<cfargument name="isRenewal" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.strReturn = { subXML=duplicate(arguments.subXML) }>

		<cfset local.topUID = XMLSearch(local.strReturn.subXML,"string(//set[@id=0]/subscription/@uid)")>
		<cfset local.currFreqID = val(xmlSearch(local.strReturn.subXML,"string(//set[@id=0]/subscription/@freqid)"))>

		<cfif local.currFreqID neq arguments.origFreqID>
			<!--- loop through the subscriptions and check the frequency --->
			<cfloop array="#XMLSearch(local.strReturn.subXML,'//subscription')#" index="local.thisSub">
				<cfif local.thisSub.xmlAttributes.uid neq local.topUID AND local.thisSub.xmlAttributes.userates eq 1>
					<cfset local.qryLoopRates = getRateQuery(memberID=local.strReturn.subXML.xmlRoot.process.xmlAttributes.memberid,
						siteID=arguments.siteID, isRoot=false, subID=local.thisSub.xmlAttributes.id, currFreqID=local.currFreqID,
						currRateID=local.thisSub.xmlAttributes.rateID, currRFID=local.thisSub.xmlAttributes.rfid, isRenewal=arguments.isRenewal,
						autoSelectRateMatch=true)>
					<cfif local.qryLoopRates.recordCount is 1>
						<cfset local.strAddRateResult = addRate(subXML=local.strReturn.subXML, parentUID=local.thisSub.xmlAttributes.uid, rfid=local.qryLoopRates.rfid)>
						<cfset local.strReturn.subXML = local.strAddRateResult.subXML>
					<cfelseif local.qryLoopRates.recordCount gt 1 and local.qryLoopRates.rateID eq local.thisSub.xmlAttributes.rateID>
						<cfset local.strAddRateResult = addRate(subXML=local.strReturn.subXML, parentUID=local.thisSub.xmlAttributes.uid, rfid=local.qryLoopRates.rfid)>
						<cfset local.strReturn.subXML = local.strAddRateResult.subXML>
					<cfelse>
						<cfset local.thisSub.xmlAttributes.rfid = 0>
						<cfset local.thisSub.xmlAttributes.rateID = 0>
						<cfset local.thisSub.xmlAttributes.rateamt = 0>
						<cfset local.thisSub.xmlAttributes.fullrateamt = 0>
						<cfset local.thisSub.xmlAttributes.rateInstallments = 1>
						<cfset local.thisSub.xmlAttributes.rateInterval = 1>
						<cfset local.thisSub.xmlAttributes.freqid = 0>
						<cfset local.thisSub.xmlAttributes.freq = ''>
						<cfset local.thisSub.xmlAttributes.forceUpfront = 0>
						<cfset local.thisSub.xmlAttributes.rateGLAccountID = 0>
						<cfset local.thisSub.xmlAttributes.feRateDescription = "">
					</cfif>
				</cfif>
			</cfloop>								
		</cfif>				

		<cfreturn local.strReturn>			
	</cffunction>

	<cffunction name="addSet" access="private" output="no" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="parentUID" type="string" required="true">
		<cfargument name="setid" type="numeric" required="true">
		<cfargument name="setname" type="string" required="true">
		<cfargument name="feAllowSelect" type="numeric" required="false" default="0">
		<cfargument name="feAllowChangePrice" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = { setUID="", subXML=duplicate(arguments.subXML) }>

		<cfset local.subNodes = xmlSearch(local.strReturn.subXML,"//subscription[@uid = '#arguments.parentUID#']")>
		<cfif arrayLen(local.subNodes)>
			<cfif xmlSearch(local.subNodes[1],"count(set[@id = '#arguments.setid#'])") is 0>
				<cfset local.subNode = local.subNodes[1]>
				<cfset arrayAppend(local.subNode.xmlChildren,XMLElemNew(local.strReturn.subXML,"set"))>
				<cfset local.newSetUID = CreateUUID()>
				<cfset local.subNode.xmlChildren[arrayLen(local.subNode.xmlChildren)].xmlAttributes["id"] = arguments.setid>
				<cfset local.subNode.xmlChildren[arrayLen(local.subNode.xmlChildren)].xmlAttributes["name"] = arguments.setname>
				<cfset local.subNode.xmlChildren[arrayLen(local.subNode.xmlChildren)].xmlAttributes["feAllowSelect"] = arguments.feAllowSelect>
				<cfset local.subNode.xmlChildren[arrayLen(local.subNode.xmlChildren)].xmlAttributes["feAllowChangePrice"] = arguments.feAllowChangePrice>
				<cfset local.subNode.xmlChildren[arrayLen(local.subNode.xmlChildren)].xmlAttributes["uid"] = local.newSetUID>

				<cfset local.strReturn.setUID = local.newSetUID>
			<cfelse>
				<cfset local.strReturn.setUID = xmlSearch(local.subNodes[1],"string(set[@id = '#arguments.setid#']/@uid)")>
			</cfif>
		</cfif>
		
		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="addSub" access="private" output="no" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="parentUID" type="string" required="true">
		<cfargument name="subID" type="numeric" required="true">
		<cfargument name="subName" type="string" required="true">
		<cfargument name="subTermFlag" type="string" required="true">
		<cfargument name="GLAccountIDToUse" type="numeric" required="true">
		<cfargument name="allowRateGLOverride" type="numeric" required="true">
		<cfargument name="payOrder" type="numeric" required="true">
		<cfargument name="activationOptionCode" type="string" required="true">
		<cfargument name="alternateActivationOptionCode" type="string" required="true">
		<cfargument name="alreadySub" type="boolean" required="false" default="false">
		<cfargument name="pcnumfree" type="numeric" required="false" default="0">
		<cfargument name="pcpctoff" type="string" required="false" default="0">
		<cfargument name="pcfree" type="boolean" required="false" default="false">
		<cfargument name="currstatus" type="string" required="false" default="">
		<cfargument name="subscriberID" type="numeric" required="false" default="0">
		<cfargument name="hasInvoiceID" type="boolean" required="false" default="false">
		<cfargument name="numQualifiedRates" type="numeric" required="false" default="-1">

		<cfset var local = structNew()>
		<cfset local.strReturn = { subUID="", subXML=duplicate(arguments.subXML) }>

		<cfset local.setNodes = xmlSearch(local.strReturn.subXML,"//set[@uid = '#arguments.parentUID#']")>
		<cfset local.xmlMemberID = local.strReturn.subXML.xmlRoot.process.xmlAttributes.memberid>
		<cfset local.useRenewalRates = local.strReturn.subXML.xmlRoot.process.xmlAttributes.useRenewalRates>

		<cfif arrayLen(local.setNodes)>
			<cfif xmlSearch(local.setNodes[1],"count(subscription[@id = '#arguments.subID#'])") is 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvProfile">
					select invoiceProfileID
					from dbo.tr_GLAccounts
					where GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.GLAccountIDToUse#">
				</cfquery>

				<cfif arguments.numQualifiedRates gte 0>
					<cfset local.numQualifiedRates = arguments.numQualifiedRates>
				<cfelse>
					<cfset local.numQualifiedRates = getQualifiedRateCount(subscriptionID=arguments.subID, memberID=local.xmlMemberID, useRenewalRates=local.useRenewalRates)>
				</cfif>

				<cfset local.setNode = local.setNodes[1]>
				<cfset arrayAppend(local.setNode.xmlChildren,XMLElemNew(local.strReturn.subXML,"subscription"))>
				<cfset local.newUID = createUUID()>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["uid"] = local.newUID>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["id"] = arguments.subID>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["name"] = arguments.subName>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["userates"] = 1>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["glaccountidtouse"] = arguments.GLAccountIDToUse>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["subGLAccountID"] = arguments.GLAccountIDToUse>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["subInvoiceProfileID"] = val(local.qryInvProfile.invoiceProfileID)>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["allowRateGLOverride"] = arguments.allowRateGLOverride>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateGLAccountID"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateInvoiceProfileID"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["termflag"] = arguments.subTermFlag>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["origRFID"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rfid"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateID"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateName"] = "">
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["numQualifiedRates"] = local.numQualifiedRates>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["feRateDescription"] = "">
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["lastPrice"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateamt"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["fullrateamt"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateInstallments"] = 1>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["rateInterval"] = 1>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["freqid"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["freq"] = ''>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["startDateOverride"] = ''>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["endDateOverride"] = ''>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["graceEndDateOverride"] = ''>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["recogStartDate"] = ''>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["recogEndDate"] = ''>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["forceUpfront"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["pcisfree"] = arguments.pcfree>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["pcrateamt"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["pcpctoff"] = arguments.pcpctoff>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["alreadysub"] = arguments.alreadySub>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["hasinvoiceid"] = arguments.hasInvoiceID>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["currstatus"] = arguments.currstatus>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["payOrder"] = arguments.payOrder>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["sid"] = arguments.subscriberID>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["pricechanged"] = 0>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["activationoptioncode"] = arguments.activationOptionCode>
				<cfset local.setNode.xmlChildren[arrayLen(local.setNode.xmlChildren)].xmlAttributes["altactivationoptioncode"] = arguments.alternateActivationOptionCode>

				<cfset local.strReturn.subUID = local.newUID>
			<cfelse>
				<cfset local.subXMLArray = xmlSearch(local.setNodes[1],"subscription[@id = '#arguments.subID#']")>
				<cfset local.thisSub = local.subXMLArray[1]>
				<cfset local.retValue = StructDelete(local.thisSub.xmlAttributes, "deleteme")>
				<cfif local.thisSub.xmlAttributes.currStatus eq 'D'>
					<cfset local.parentsetNodes = xmlSearch(local.strReturn.subXML,"//set[@id = '0']")>
					<cfif arrayLen(local.parentsetNodes)>
						<cfset local.parentsetNode = local.parentsetNodes[1]>
						<cfset local.parentStatus = local.parentsetNode.xmlChildren[1].xmlAttributes.currstatus>
						<cfset local.thisSub.xmlAttributes.currStatus = local.parentStatus>
						<cfset local.thisSub.xmlAttributes["readdme"] = 1>
					</cfif>
				</cfif>

				<cfset local.strReturn.subUID = xmlSearch(local.setNodes[1],"string(subscription[@id = '#arguments.subID#']/@uid)")>
			</cfif>
		<cfelse>
			<cfset local.strReturn.subUID = "">
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>	

	<cffunction name="removeSub" access="private" output="no" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="subUID" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { subXML=duplicate(arguments.subXML) }>

		<cfset local.subNodes = xmlSearch(local.strReturn.subXML,"//subscription[@uid = '#arguments.subUID#']")>
		<cfif arrayLen(local.subNodes)>
			<cfset local.subNode = local.subNodes[1]>
			<cfset local.subNode.xmlAttributes["deleteme"] = 1>
			<cfif (local.subNode.xmlAttributes.currstatus eq "R") OR (local.subNode.xmlAttributes.currstatus eq "O")>
				<cfset local.subNode.xmlAttributes.pcisfree = false>
			</cfif>

			<!--- if this sub had any children, they need to be removed as well --->
			<cfset local.childSubNodes = xmlSearch(local.subNode,".//subscription")>
			<cfif arrayLen(local.childSubNodes)>
				<cfloop array="#local.childSubNodes#" index="local.childSub">
					<cfset local.strRemoveSubResult = removeSub(subXML=local.strReturn.subXML, subUID=local.childSub.xmlAttributes.uid)>
					<cfset local.strReturn.subXML = duplicate(local.strRemoveSubResult.subXML)>
				</cfloop>
			</cfif>

			<cfset local.setNodes = xmlSearch(local.subNode,"../")>
			<cfif arrayLen(local.setNodes)>
				<cfset local.setNode = local.setNodes[1]>
				<cfloop from="#arrayLen(local.setNode.xmlChildren)#" to="1" step="-1" index="local.nodeidx">
					<cfif StructKeyExists(local.setNode.xmlChildren[local.nodeIdx].XmlAttributes,"deleteme")>
						<cfif local.setNode.xmlChildren[local.nodeIdx].XmlAttributes.sid eq 0>
							<cfset ArrayDeleteAt(local.setNode.xmlChildren,local.nodeIdx)>
						</cfif>
						<cfbreak>
					</cfif>
				</cfloop>
				
				<!--- remove sub's parent set if set is empty now --->
				<cfif arrayLen(local.setNode.xmlChildren) is 0>
					<cfset local.setNode.xmlAttributes["deleteme"] = 1>
					<cfset local.setParentNodes = xmlSearch(local.setNode,"../")>
					<cfif arrayLen(local.setParentNodes)>
						<cfset local.setParentNode = local.setParentNodes[1]>
						<cfloop from="#arrayLen(local.setParentNode.xmlChildren)#" to="1" step="-1" index="local.nodeidx">
							<cfif StructKeyExists(local.setParentNode.xmlChildren[local.nodeIdx].XmlAttributes,"deleteme")>
								<cfset ArrayDeleteAt(local.setParentNode.xmlChildren,local.nodeIdx)>
								<cfbreak>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="addRate" access="package" output="no" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="parentUID" type="string" required="true">
		<cfargument name="rfid" type="numeric" required="true">
		<cfargument name="modifiedRate" type="string" required="false" default="">
		<cfargument name="lastPrice" type="string" required="false" default="">
		<cfargument name="origRFID" type="string" required="false" default="">
		<cfargument name="recogStartDate" type="string" required="false" default="">
		<cfargument name="recogEndDate" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.strReturn = { subXML=duplicate(arguments.subXML) }>

		<cftry>
			<cfset local.subNodes = xmlSearch(local.strReturn.subXML,"//subscription[@uid = '#arguments.parentUID#']")>
			<cfset local.xmlMemberID = local.strReturn.subXML.xmlRoot.process.xmlAttributes.memberid>
			<cfset local.xmlSubscriptionID = local.subNodes[1].xmlAttributes.id>

			<cfif Len(arguments.lastPrice) gt 0>
				<cfset local.subNodes[1].xmlAttributes.lastPrice = arguments.lastPrice>
			</cfif>
			<cfif Len(arguments.origRFID)>
				<cfset local.subNodes[1].xmlAttributes.origRFID = val(arguments.origRFID)>
			</cfif>
			<cfif len(arguments.recogStartDate) and len(arguments.recogEndDate)>
				<cfset local.subNodes[1].xmlAttributes.recogStartDate = arguments.recogStartDate>
				<cfset local.subNodes[1].xmlAttributes.recogEndDate = arguments.recogEndDate>
			</cfif>
			
			<cfquery name="local.qryRateAmt" datasource="#application.dsn.membercentral.dsn#">
				select rf.rfid, rf.rateAmt, rfMain.rateAmt as fullRateAmt, f.frequencyName, f.frequencyID, 
					f.hasInstallments, r.rateName, r.forceUpfront, r.GLAccountID, gl.invoiceProfileID, r.rateID,r.frontEndAllowChangePrice,r.frontEndChangePriceMin,r.frontEndChangePriceMax,
					case when f.hasInstallments = 1 then f.monthlyInterval else 1 end as monthlyInterval,
					case when f.hasInstallments = 1 then rf.numInstallments else 1 end as numInstallments,
					r.recogAFStartDate as recogStartDate, r.recogAFEndDate as recogEndDate
				from dbo.sub_rateFrequencies as rf 
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				inner join dbo.sub_rates r on r.rateID = rf.rateID
				inner join dbo.sub_frequencies as fMain on fMain.siteID = f.siteID and fMain.frequencyShortName = 'F'
				inner join dbo.sub_rateFrequencies as rfMain on rfMain.rateID = rf.rateID and rfMain.frequencyID = fMain.frequencyID
				left outer join dbo.tr_GLAccounts gl on gl.GLAccountID = r.GLAccountID
				where rf.rfid = <cfqueryparam value="#arguments.rfid#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfif local.qryRateAmt.recordCount gt 0>
				<cfset local.subNodes[1].xmlAttributes.rfid = local.qryRateAmt.rfid>
				<cfset local.subNodes[1].xmlAttributes.rateID = local.qryRateAmt.rateID>
				<cfset local.subNodes[1].xmlAttributes.rateName = local.qryRateAmt.rateName>
				<cfset local.subNodes[1].xmlAttributes.rateamt = local.qryRateAmt.rateAmt>
				<cfset local.subNodes[1].xmlAttributes.fullrateamt = local.qryRateAmt.fullrateAmt>
				<cfset local.subNodes[1].xmlAttributes.rateInstallments = local.qryRateAmt.numInstallments>
				<cfset local.subNodes[1].xmlAttributes.rateInterval = local.qryRateAmt.monthlyInterval>
				<cfset local.subNodes[1].xmlAttributes.freqid = local.qryRateAmt.frequencyID>
				<cfset local.subNodes[1].xmlAttributes.freq = local.qryRateAmt.frequencyName>
				<cfset local.subNodes[1].xmlAttributes.forceUpfront = local.qryRateAmt.forceUpfront>
				<cfset local.subNodes[1].xmlAttributes.rateGLAccountID = val(local.qryRateAmt.GLAccountID)>

				<cfif local.subNodes[1].xmlAttributes.rateGLAccountID and local.subNodes[1].xmlAttributes.allowRateGLOverride>
					<cfset local.subNodes[1].xmlAttributes.glaccountidtouse = local.subNodes[1].xmlAttributes.rateGLAccountID>
				<cfelseif local.subNodes[1].xmlAttributes.allowRateGLOverride AND NOT local.subNodes[1].xmlAttributes.rateGLAccountID>
					<!--- Look up GLAccountID for root subscription if not set on rate --->
					<cfquery name="local.qrySubGLAccount" datasource="#application.dsn.membercentral.dsn#">
						select GLAccountID 
						from dbo.sub_subscriptions 
						where subscriptionid = <cfqueryparam value="#local.subNodes[1].xmlAttributes.id#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>	
					<cfset local.subNodes[1].xmlAttributes.glaccountidtouse = local.qrySubGLAccount.GLAccountID>
				<cfelse>
					<cfset local.subNodes[1].xmlAttributes.glaccountidtouse = local.subNodes[1].xmlAttributes.subGLAccountID>
				</cfif>

				<cfif NOT len(arguments.recogStartDate) or NOT len(arguments.recogEndDate)>
					<!--- Recognition date range checking --->
					<cfset local.rootTermDateRFID = xmlSearch(local.strReturn.subXML,"number(//set[@id='0']/subscription/@rfid)")>
					<cfset local.rootSubTermFlag = xmlSearch(local.strReturn.subXML,"string(//set[@id='0']/subscription/@termflag)")>
					<cfset local.existingSubscriberID = val(xmlSearch(local.strReturn.subXML,"string(//set[@id=0]/subscription/@sid)"))>
					
					<!--- Check for overridden dates --->
					<cfset local.subTermStartDate = xmlSearch(local.strReturn.subXML,"string(//set[@id='0']/subscription/@startDateOverride)")>
					<cfset local.subTermEndDate = xmlSearch(local.strReturn.subXML,"string(//set[@id='0']/subscription/@endDateOverride)")>

					<cfif (len(local.subTermStartDate) eq 0) OR (len(local.subTermEndDate) eq 0)>
						<cfif local.existingSubscriberID>
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
								select subStartDate, subEndDate, graceEndDate
								from dbo.sub_subscribers
								where subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.existingSubscriberID#">
							</cfquery>
							<cfset local.subTermStartDate = DateFormat(local.qrySubDates.subStartDate, "m/d/yyyy")>
							<cfset local.subTermEndDate = DateFormat(local.qrySubDates.subEndDate, "m/d/yyyy")>
						<cfelse>
							<cfset local.subTermDates = getSubTermDates(termDateRFID=local.roottermDateRFID, subTermFlag=local.rootsubTermFlag)>
							<cfset local.subTermStartDate = local.subTermDates.subTermStartDate>
							<cfset local.subTermEndDate = local.subTermDates.subTermEndDate>
						</cfif>
					</cfif>				

					<cfset local.recogDateStruct = getRecognitionDates(useRFID=local.qryRateAmt.rfid, saleTransactionDate=now(), 
						subStartDate=local.subTermStartDate, subEndDate=local.subTermEndDate, rootsubTermFlag=local.rootsubTermFlag)>
					<cfset local.subNodes[1].xmlAttributes.recogStartDate = local.recogDateStruct.recogStartDate>
					<cfset local.subNodes[1].xmlAttributes.recogEndDate = local.recogDateStruct.recogEndDate>
				</cfif>
				<cfset local.subNodes[1].xmlAttributes.rateInvoiceProfileID = val(local.qryRateAmt.invoiceProfileID)>
				<cfset local.subNodes[1].xmlAttributes["frontEndAllowChangePrice"] = local.qryRateAmt.frontEndAllowChangePrice>
				<cfset local.subNodes[1].xmlAttributes["frontEndChangePriceMin"] = local.qryRateAmt.frontEndChangePriceMin>
				<cfset local.subNodes[1].xmlAttributes["frontEndChangePriceMax"] = local.qryRateAmt.frontEndChangePriceMax>

				<cfset local.topUID = XMLSearch(local.strReturn.subXML,"string(//set[@id=0]/subscription/@uid)")>
				<cfset local.currFreqID = val(xmlSearch(local.strReturn.subXML,"string(//set[@id=0]/subscription/@freqid)"))>
				<cfset local.numPaymentsToUse = val(xmlSearch(local.strReturn.subXML,"string(//set[@id=0]/subscription/@rateInstallments)"))>

				<!--- If the rate is modifed to match the FullFreq Rate then remove modified rate and switch subscriber back to standard pricing --->
				<cfif val(arguments.modifiedRate) GT 0 AND arguments.modifiedRate eq local.qryRateAmt.fullRateAmt>
					<cfset arguments.modifiedRate = "">
					<cfset local.subNodes[1].xmlAttributes.fullRateAmt = local.qryRateAmt.fullRateAmt>
					<cfset local.subNodes[1].xmlAttributes.rateamt = local.qryRateAmt.rateAmt>
					<cfset local.subNodes[1].xmlAttributes.userates = 1>
					<cfset local.subNodes[1].xmlAttributes.lastPrice = "">
					<cfset local.subNodes[1].xmlAttributes.pcrateamt = 0>
					<cfset local.subNodes[1].xmlAttributes.pricechanged = 0>
				</cfif>

				<cfif (arguments.rfid eq local.subNodes[1].xmlAttributes.origRFID) AND len(local.subNodes[1].xmlAttributes.lastPrice)>
					<cfset local.subNodes[1].xmlAttributes.fullrateamt = local.subNodes[1].xmlAttributes.lastPrice>
					<cfset local.subNodes[1].xmlAttributes.rateamt = local.subNodes[1].xmlAttributes.lastPrice / local.qryRateAmt.numInstallments>
					<cfset local.rateAmtToUse = local.subNodes[1].xmlAttributes.rateamt>
					<cfset local.rateInstallmentsToUse = local.qryRateAmt.numInstallments>
				<cfelseif (local.qryRateAmt.frequencyID neq local.currFreqID)>
					<cfset local.rateAmtToUse = local.qryRateAmt.fullrateAmt>
					<cfset local.rateInstallmentsToUse = 1>
				<cfelse>
					<cfset local.rateAmtToUse = local.qryRateAmt.rateAmt>
					<cfset local.rateInstallmentsToUse = local.qryRateAmt.numInstallments>
				</cfif>

				<cfif local.qryRateAmt.forceUpfront>
					<cfset local.numPaymentsToUse = 1>
					<cfset local.rateAmtToUse = local.qryRateAmt.fullRateAmt>
					<cfset local.rateInstallmentsToUse = 1>
				</cfif>

				<cfset local.rateToUse = getRateToUse(useRates=(val(arguments.modifiedRate) eq 0), rateAmt=local.rateAmtToUse, 
					rateInstallments=local.rateInstallmentsToUse, numPaymentsToUse=local.numPaymentsToUse, 
					pcPctOff= local.subNodes[1].xmlAttributes.pcpctoff, pcRateAmt=val(arguments.modifiedRate))>

				<cfsavecontent variable="local.feRateDescription">
					<cfoutput>				
						<cfif local.qryRateAmt.numInstallments neq local.numPaymentsToUse>
							#local.numPaymentsToUse# payment<cfif local.numPaymentsToUse gt 1>s</cfif> of
						<cfelse>
							#local.numPaymentsToUse# payment<cfif local.numPaymentsToUse gt 1>s</cfif> of
						</cfif>
						#dollarformat(local.rateToUse.rateToUse)#
						<cfif (local.numPaymentsToUse neq 1) AND (local.rateToUse.rateTotal gt 0)>
							(#dollarformat(local.rateToUse.rateTotal)# total)
						</cfif>
					</cfoutput>
				</cfsavecontent>
				
				<cfset local.subNodes[1].xmlAttributes.feRateDescription = rereplaceNocase(local.feRateDescription,"\s{2,}"," ","all")>
			</cfif>
			<cfif Len(arguments.modifiedRate) gt 0>
				<cfset local.subNodes[1].xmlAttributes.userates = 0>
				<cfset local.subNodes[1].xmlAttributes.pcrateamt = arguments.modifiedRate>
				<cfset local.subNodes[1].xmlAttributes.pricechanged = 1>
			<cfelse>
				<cfset local.subNodes[1].xmlAttributes.userates = 1>
			</cfif>

		<cfcatch type="any">
			<cfset local.arguments = arguments>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="model.admin.subscriptions.subscriptionReg.addRate + rethrow")>
			<cfrethrow>
		</cfcatch>
		</cftry>	

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="addInvoice" access="private" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="invoiceID" type="numeric" required="true">
		<cfargument name="dateDue" type="string" required="true">
		<cfargument name="invoiceProfileID" type="numeric" required="true">
		<cfargument name="sumTotal" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { subXML=duplicate(arguments.subXML), invUID="" }>

		<cfset local.procNodes = xmlSearch(local.strReturn.subXML,"//process")>
		<cfif arrayLen(local.procNodes)>
			<cfif xmlSearch(local.procNodes[1],"count(invoice[@id = '#arguments.invoiceID#'])") is 0>
				<cfset local.procNode = local.procNodes[1]>
				<cfset arrayAppend(local.procNode.xmlChildren,XMLElemNew(local.strReturn.subXML,"invoice"))>
				<cfset local.procNode.xmlChildren[arrayLen(local.procNode.xmlChildren)].xmlAttributes["uid"] = createUUID()>
				<cfset local.procNode.xmlChildren[arrayLen(local.procNode.xmlChildren)].xmlAttributes["id"] = arguments.invoiceID>
				<cfset local.procNode.xmlChildren[arrayLen(local.procNode.xmlChildren)].xmlAttributes["datedue"] = arguments.dateDue>
				<cfset local.procNode.xmlChildren[arrayLen(local.procNode.xmlChildren)].xmlAttributes["invoiceprofileid"] = arguments.invoiceProfileID>
				<cfset local.procNode.xmlChildren[arrayLen(local.procNode.xmlChildren)].xmlAttributes["sumtotal"] = arguments.sumTotal>
				<cfset local.invUID = local.procNode.xmlChildren[arrayLen(local.procNode.xmlChildren)].xmlAttributes.uid>
			<cfelse>
				<cfset local.strReturn.invUID = xmlSearch(local.procNodes[1],"string(invoice[@id = '#arguments.invoiceID#']/@uid)")>
			</cfif>
		<cfelse>
			<cfset local.strReturn.invUID = "">
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getSubTreeVerbose" access="private" output="false" returntype="string">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="termDateString" type="string" required="false" default="">
		<cfargument name="isEmail" type="numeric" required="false" default="0">
		<cfargument name="useAccrualAccounting" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.xmlRoot = arguments.subXML.xmlRoot>

		<cfsavecontent variable="local.returnString">
			<cfif xmlsearch(arguments.subXML,"count(//set/subscription)") gt 0>
				<cfset local.rootUID = xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@uid)")>
				<cfset local.rootStatus = xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@currstatus)")>
				<cfoutput>
				<cfif len(arguments.termDateString)>
					<div class="row no-gutters">
						<div class="col-sm-6">Subscription Term:</div>
						<div class="col-sm-6 px-2 text-right">
							<span class="font-weight-bold">#arguments.termDateString#</span>
							<cfif (NOT Len(local.rootStatus) OR listFindNoCase("R,O",local.rootStatus)) AND arguments.isEmail eq 0>
								<a href="javascript:changeDates('#local.rootUID#');">
									<i class="fa-solid fa-calendar-pen font-size-lg text-primary"></i>
								</a>
							</cfif>
						</div>
					</div>
				</cfif>
				<table class="table table-sm table-borderless mt-2">
				#getSubTreeVerboseRow(subXML=arguments.subXML, node=local.xmlRoot.set, level=0, useAccrualAccounting=arguments.useAccrualAccounting, isEmail=arguments.isEmail)#
				</table>
				</cfoutput>
			</cfif>
		</cfsavecontent>

		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="getSubTreeVerboseRow" access="private" output="false" returntype="string">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="node" type="xml" required="yes">
		<cfargument name="level" type="numeric" required="yes">
		<cfargument name="useAccrualAccounting" type="boolean" required="yes">
		<cfargument name="isEmail" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.returnString">
			<cfoutput>
			<cfloop array="#arguments.node.xmlChildren#" index="local.childNode">
				<cfif local.childNode.xmlName eq "subscription">
					<cfif (local.childNode.xmlAttributes.currstatus eq 'D') OR (StructKeyExists(local.childNode.xmlAttributes,"deleteme") AND local.childNode.xmlAttributes.deleteme eq 1)>
						<cfset local.trstyle = "background-color: ##e8aeae;">
					<cfelseif local.childNode.xmlAttributes.forceUpfront eq 1>
						<cfset local.trstyle = "background-color: ##d7e5f5;">
					<cfelse>
						<cfset local.trstyle = "">
					</cfif>
					<tr style="#local.trstyle#">
						<td class="align-top">
							<cfif arguments.isEmail eq 0>
								<a href="javascript:showRates('#local.childNode.xmlAttributes.uid#')"><b>#local.childNode.xmlAttributes.name#</b></a>
								<cfelse>
								<b>#local.childNode.xmlAttributes.name#</b>
							</cfif>
						</td>
						<td>&nbsp;</td>
						<cfif (local.childNode.xmlAttributes.alreadysub eq true) 
									AND (local.childNode.xmlAttributes.currstatus neq 'R') 
									AND (local.childNode.xmlAttributes.currstatus neq 'O')
									AND (local.childNode.xmlAttributes.hasinvoiceid eq 'true')
									AND NOT (StructKeyExists(local.childNode.xmlAttributes, "readdme") AND (local.childNode.xmlAttributes.readdme neq 1))>
								<td class="align-top" colspan="2">Previous</td>
						<cfelseif (local.childNode.xmlAttributes.currstatus eq 'D')>
								<td colspan="2">&nbsp;</td>
						<cfelse>
							<cfif local.childNode.xmlAttributes.userates is 1>
								<cfif local.childNode.xmlAttributes.rfid gt 0>
									<cfset local.loopRateToUse = getRateToUse(useRates=local.childNode.xmlAttributes.userates, rateAmt=local.childNode.xmlAttributes.rateAmt,
										rateInstallments=local.childNode.xmlAttributes.rateInstallments, numPaymentsToUse=local.childNode.xmlAttributes.rateInstallments,
										pcPctOff=local.childNode.xmlAttributes.pcpctoff, pcRateAmt=local.childNode.xmlAttributes.pcrateamt)>
								
									<cfif local.childNode.xmlAttributes.pcisfree eq "false">
										<td class="align-top text-right">#DollarFormat(local.loopRateToUse.rateToUse)#</td>
										<td>&nbsp;<cfif local.childNode.xmlAttributes.rateAmt gt 0>#local.childNode.xmlAttributes.freq#</cfif></td>
									<cfelse>
										<td class="align-top text-right">#DollarFormat(0)#</td>
										<td class="align-top">Free</td>
									</cfif>
								<cfelse>
									<td colspan="2"><a href="javascript:showRates('#local.childNode.xmlAttributes.uid#')">Choose rate</a></td>
								</cfif>
							<cfelse>
								<cfset local.rateToUse = local.childNode.xmlAttributes.pcrateamt>

								<cfif local.childNode.xmlAttributes.pcisfree eq "false">
									<td class="align-top text-right">#DollarFormat(local.rateToUse)#</td>
									<td>&nbsp;</td>
								<cfelse>
									<td class="text-right">#DollarFormat(0)#</td>
									<td class="align-top">Free</td>
								</cfif>
							</cfif>
						</cfif>
					</tr>
					<tr style="#local.trstyle#">
						<td class="align-top" colspan="4">
							<div class="ml-4">
								<cfif local.childNode.xmlAttributes.useRates eq 1>
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateName">
										select r.rateName
										from dbo.sub_rateFrequencies rf
										inner join dbo.sub_rates r on r.rateID = rf.rateID
										where rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.childNode.xmlAttributes.rfid#">
									</cfquery>
									Rate: #local.qryRateName.rateName#
								<cfelse>
									Rate Overridden
								</cfif>
							</div>
							<div class="mt-2 ml-4">
								<cfif local.childNode.xmlAttributes.allowRateGLOverride AND local.childNode.xmlAttributes.rateGLAccountID neq 0>
									<cfset local.loopInvProfileID = local.childNode.xmlAttributes.rateInvoiceProfileID>
								<cfelse>
									<cfset local.loopInvProfileID = local.childNode.xmlAttributes.subInvoiceProfileID>
								</cfif>
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoiceProfile">
									select profileName
									from dbo.tr_invoiceProfiles
									where profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.loopInvProfileID)#">
								</cfquery>
								Invoice Profile: #local.qryInvoiceProfile.profileName#
							</div>
							<cfif arguments.useAccrualAccounting is 1 and len(local.childNode.xmlAttributes.recogStartDate)>
								<cfset local.rootSubRecogStartDate = xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@recogStartDate)")>
								<cfset local.rootSubRecogEndDate = xmlSearch(arguments.subXML,"string(//set[@id=0]/subscription/@recogEndDate)")>
								<cfif not dateCompare(local.rootSubRecogStartDate,local.childNode.xmlAttributes.recogStartDate, "d") AND not dateCompare(local.rootSubRecogEndDate,local.childNode.xmlAttributes.recogEndDate, "d")>
									<cfset local.spanClass = "">									
								<cfelse>
									<cfset local.spanClass = " class='font-weight-bold text-danger'">									
								</cfif>

								<div class="mt-2 ml-4">
									Recognition Dates: 
									<span#local.spanClass#>
										#dateformat(local.childNode.xmlAttributes.recogStartDate,'m/d/yyyy')# - #dateformat(local.childNode.xmlAttributes.recogEndDate,'m/d/yyyy')#
										(#dateDiff("m", local.childNode.xmlAttributes.recogStartDate, "#local.childNode.xmlAttributes.recogEndDate#") + 1# months)
									</span>
								</div>
							</cfif>
						</td>
					</tr>

				<cfelseif local.childNode.xmlName eq "set">
					<cfset local.showSet = false>
					<cfloop array="#local.childNode.xmlChildren#" index="local.setChildNode">
						<cfif (NOT structKeyExists(local.setChildNode.xmlAttributes, "deleteme") OR (local.setChildNode.xmlAttributes.deleteme neq "1"))>
							<cfset local.showSet = true>
							<cfbreak>
						</cfif>
					</cfloop>
					<cfset local.parentSubUID = xmlSearch(arguments.subXML,"string(//subscription[set[@uid = '#local.childNode.xmlAttributes.uid#']]/@uid)")>
					<tr><td colspan="4">&nbsp;</td></tr>
					<tr>
						<td class="align-top">
							<cfif arguments.isEmail eq 0>	
								<a href="javascript:showAddons('#local.parentSubUID#')"><b>#local.childNode.xmlAttributes.name#</b></a>
							<cfelse>
								<b>#local.childNode.xmlAttributes.name#</b>
							</cfif>							
						</td>
						<td width="30">&nbsp;</td>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
					</tr>
				</cfif>

				<cfif local.childNode.xmlName eq "set" or arrayLen(local.childNode.xmlChildren)>
					#getSubTreeVerboseRow(subXML=arguments.subXML, node=local.childNode, level=(arguments.level+1), useAccrualAccounting=arguments.useAccrualAccounting, isEmail=arguments.isEmail)#
					<cfif local.childNode.xmlName eq "subscription" and arrayLen(local.childNode.xmlChildren)>
						<tr><td colspan="4">&nbsp;</td></tr>
					</cfif>
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="getSubTreeVerboseWithPrices" access="private" output="false" returntype="string">
		<cfargument name="subXML" type="xml" required="yes">

		<cfset var local = structNew()>
		<cfset local.xmlRoot = arguments.subXML.xmlRoot>

		<cfsavecontent variable="local.returnString">
			<cfif xmlsearch(arguments.subXML,"count(//set/subscription)") gt 0>
				<cfoutput>
				<table class="table table-sm table-borderless">
					<thead>
						<tr>
							<th>Subscription</th>
							<th class="text-right" colspan="3">Amount Remaining</th>
							<th>&nbsp;</th>
							<th>&nbsp;</th>
							<th colspan="2">New Amount</th>
						</tr>
					</thead>
					<tbody>
						#getSubTreeVerboseWithPricesRow(node=local.xmlRoot.set, level=0)#
					</tbody>
				</table>
				</cfoutput>
			</cfif>
		</cfsavecontent>

		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="getSubTreeVerboseWithPricesRow" access="private" output="false" returntype="string">
		<cfargument name="node" type="xml" required="yes">
		<cfargument name="level" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfsavecontent variable="local.returnString">
			<cfloop array="#arguments.node.xmlChildren#" index="local.childNode">
				<cfif local.childNode.xmlName eq "subscription">
					<cfset local.subIDToUse = Replace(local.childNode.xmlAttributes.uid, "-", "_", "all")>
					<cfoutput>
					<tr 
						<cfif (local.childNode.xmlAttributes.currstatus eq 'D') OR (StructKeyExists(local.childNode.xmlAttributes,"deleteme") AND local.childNode.xmlAttributes.deleteme eq 1)>
							style="background-color: ##e8aeae;"
						<cfelseif local.childNode.xmlAttributes.forceUpfront eq 1>
							style="background-color: ##d7e5f5;"
						</cfif>>
						<td class="align-top">
							<cfif (local.childNode.xmlAttributes.alreadysub neq true) 
									OR (local.childNode.xmlAttributes.currstatus eq 'R') 
									OR (local.childNode.xmlAttributes.currstatus eq 'O')>
								<a href="javascript:showRates('#local.childNode.xmlAttributes.uid#')">#local.childNode.xmlAttributes.name#</a>
							<cfelse>
								#local.childNode.xmlAttributes.name#
							</cfif>
							<div class="mt-2 ml-4 font-italic">
								<cfif local.childNode.xmlAttributes.useRates eq 1>
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateName">
										select r.rateName
										from dbo.sub_rateFrequencies rf
										inner join dbo.sub_rates r on r.rateID = rf.rateID
										where rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.childNode.xmlAttributes.rfid#">
									</cfquery>
									#local.qryRateName.rateName#
								<cfelse>
									Rate Overridden
								</cfif>
							</div>
						</td>
						<td>&nbsp;</td>
						<cfif (local.childNode.xmlAttributes.alreadysub eq true) 
									AND (local.childNode.xmlAttributes.currstatus neq 'R') 
									AND (local.childNode.xmlAttributes.currstatus neq 'O')
									AND ((local.childNode.xmlAttributes.alreadysub eq true) AND (local.childNode.xmlAttributes.hasinvoiceid eq 'true'))
									AND NOT (StructKeyExists(local.childNode.xmlAttributes, "readdme") AND (local.childNode.xmlAttributes.readdme neq 1))>
									
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubRemaining">
								SET NOCOUNT ON;

								declare @orgID int;

								IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
									DROP TABLE ##mcSubscribersForAcct;
								IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
									DROP TABLE ##mcSubscriberTransactions;

								CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
								CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
									invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
									amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
									assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

								INSERT INTO ##mcSubscribersForAcct (subscriberID)
								VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.childNode.xmlAttributes.sid#">);

								select top 1 @orgID = s.orgID
								from dbo.sub_subscribers as s
								inner join ##mcSubscribersForAcct as tmp on tmp.subscriberID = s.subscriberID;

								EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

								select s.subscriberID, s.subscriptionID, sum(st.amount) as totalRemaining
								from dbo.sub_subscribers s
								inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
								inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
								where s.orgID = @orgID
								and ins.status = 'Pending'
								group by s.subscriberID, s.subscriptionID;

								IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
									DROP TABLE ##mcSubscribersForAcct;
								IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
									DROP TABLE ##mcSubscriberTransactions;
							</cfquery>

								<td class="align-top text-right">#DollarFormat(local.qrySubRemaining.totalRemaining)#</td>
								<td colspan="3">&nbsp;</td>
								<td class="align-top text-right" colspan="2">
									<div class="input-group input-group-sm">
										<div class="input-group-prepend">
											<span class="input-group-text">$</span>
										</div>
										<input type="text" name="newRate_#local.subIDToUse#" id="newRate_#local.subIDToUse#" class="form-control form-control-sm" value="#numberformat(local.qrySubRemaining.totalRemaining, "_.__")#" onBlur="this.value=formatCurrency(this.value)">
									</div>
									<input type="hidden" name="origRate_#local.subIDToUse#" id="origRate_#local.subIDToUse#" value="#numberformat(local.qrySubRemaining.totalRemaining, "_.__")#">
								</td>
						<cfelse>
							<cfif local.childNode.xmlAttributes.userates is 1>
								<cfif local.childNode.xmlAttributes.rfid gt 0>
								
									<cfset local.loopRateToUse = getRateToUse(useRates=local.childNode.xmlAttributes.userates,
																														rateAmt=local.childNode.xmlAttributes.rateAmt,
																														rateInstallments=local.childNode.xmlAttributes.rateInstallments,
																														numPaymentsToUse=local.childNode.xmlAttributes.rateInstallments,
																														pcPctOff=local.childNode.xmlAttributes.pcpctoff,
																														pcRateAmt=local.childNode.xmlAttributes.pcrateamt)>
								
									<cfif local.childNode.xmlAttributes.pcisfree eq "false">
										<td class="align-top text-right">#DollarFormat(local.loopRateToUse.rateToUse)#</td>
										<td colspan="3">&nbsp;<cfif local.childNode.xmlAttributes.rateAmt gt 0>#local.childNode.xmlAttributes.freq#</cfif></td>
										<td class="align-top text-right" colspan="2">
											<div class="input-group input-group-sm">
												<div class="input-group-prepend">
													<span class="input-group-text">$</span>
												</div>
												<input type="text" name="newRate_#local.subIDToUse#" id="newRate_#local.subIDToUse#" class="form-control form-control-sm" value="#numberformat(local.loopRateToUse.rateTotal, "_.__")#"  onBlur="this.value=formatCurrency(this.value)">
											</div>
											<input type="hidden" name="origRate_#local.subIDToUse#" id="origRate_#local.subIDToUse#" value="#numberformat(local.loopRateToUse.rateTotal, "_.__")#">
										</td>
									<cfelse>
										<td class="align-top text-right">#DollarFormat(0)#</td>
										<td class="align-top">Free</td>
										<td colspan="2">&nbsp;</td>
										<td class="align-top text-right" colspan="2">
											<div class="input-group input-group-sm">
												<div class="input-group-prepend">
													<span class="input-group-text">$</span>
												</div>
												<input type="text" name="newRate_#local.subIDToUse#" id="newRate_#local.subIDToUse#" class="form-control form-control-sm" value="#numberformat(0, "_.__")#" onBlur="this.value=formatCurrency(this.value)">
											</div>
											<input type="hidden" name="origRate_#local.subIDToUse#" id="origRate_#local.subIDToUse#" value="#numberformat(0, "_.__")#">
										</td>
									</cfif>
								<cfelse>
									<td class="align-top" colspan="2"><a href="javascript:showRates('#local.childNode.xmlAttributes.uid#')">Choose rate</a></td>
								</cfif>
							<cfelse>
								<cfset local.rateToUse = local.childNode.xmlAttributes.pcrateamt>

								<cfif local.childNode.xmlAttributes.pcisfree eq "false">
									<td class="align-top text-right">#DollarFormat(local.rateToUse)#</td>
									<td colspan="3">&nbsp;</td>
									<td class="align-top text-right" colspan="2">
										<div class="input-group input-group-sm">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<input type="text" name="newRate_#local.subIDToUse#" id="newRate_#local.subIDToUse#" class="form-control form-control-sm" value="#numberformat(local.rateToUse, "_.__")#" onBlur="this.value=formatCurrency(this.value)">
										</div>
										<input type="hidden" name="origRate_#local.subIDToUse#" id="origRate_#local.subIDToUse#" value="#numberformat(local.rateToUse, "_.__")#">
									</td>
								<cfelse>
									<td class="align-top text-right">#DollarFormat(0)#</td>
									<td class="align-top">Free</td>
									<td colspan="2">&nbsp;</td>
									<td class="align-top text-right" colspan="2">
										<div class="input-group input-group-sm">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<input type="text" name="newRate_#local.subIDToUse#" id="newRate_#local.subIDToUse#" class="form-control form-control-sm" value="#numberformat(0, "_.__")#" onBlur="this.value=formatCurrency(this.value)">
										</div>
										<input type="hidden" name="origRate_#local.subIDToUse#" id="origRate_#local.subIDToUse#" value="#numberformat(0, "_.__")#">
									</td>
								</cfif>
							</cfif>
						</cfif>
					</tr>
					</cfoutput>
				</cfif>
				<cfif local.childNode.xmlName eq "set">
					<cfoutput>
					<tr>
						<td colspan="8">&nbsp;</td>
					</tr>
					<tr>
						<td class="font-weight-bold">#local.childNode.xmlAttributes.name#</td>
						<td width="30">&nbsp;</td>
						<td colspan="6">&nbsp;</td>
					</tr>
					</cfoutput>
				</cfif>
				<cfif local.childNode.xmlName eq "set" or arrayLen(local.childNode.xmlChildren)>
					<cfoutput>#getSubTreeVerboseWithPricesRow(node=local.childNode,level=(arguments.level+1))#</cfoutput>
				</cfif>
			</cfloop>
		</cfsavecontent>
		
		<cfreturn local.returnString>
	</cffunction>

	<cffunction name="showSubscriptions" access="public" output="false" returntype="struct">
		<cfargument name="siteid" type="numeric" required="true">
		<cfargument name="memberid" type="numeric" required="true">
		<cfargument name="filterTypeID" type="numeric" required="false" default="0">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		<cfset local.AddSubscriptionSubscriptionTypeRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionType", functionName="AddSubscription")>
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.strReturn.qrySubscriptions" result="local.qrySubscriptionsResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @FID int, @memberid int, @siteID int, @addSubscriptionFID int, @enteredByMemberID int;
			declare @allowedTypes TABLE (typeID int PRIMARY KEY);
			set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			set @addSubscriptionFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AddSubscriptionSubscriptionTypeRFID#">;
			set @siteid = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">;
			set @memberid = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">;
			set @enteredByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">;

			insert into @allowedTypes (typeID)
			SELECT t.typeID
			FROM dbo.sub_Types t
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp ON srfrp.siteID = @siteID
				AND srfrp.siteResourceID = t.siteResourceID
				AND srfrp.functionID = @addSubscriptionFID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
				AND srfrp.rightPrintID = gprp.rightPrintID
			inner join dbo.ams_members as m on m.groupPrintID = gprp.groupPrintID
				and m.memberID = @enteredByMemberID
			WHERE t.siteID = @siteID
			AND t.status = 'A'
			<cfif arguments.filterTypeID>
				AND t.typeID = <cfqueryparam value="#arguments.filterTypeID#" cfsqltype="cf_sql_integer">
			</cfif>;

			SELECT s.subscriptionID, s.typeID, s.subscriptionName, t.typeName
			FROM @allowedTypes allowed
			INNER JOIN dbo.sub_Types as t on t.typeID = allowed.typeID 
			inner join dbo.sub_subscriptions as s on t.typeID = s.typeID 
				and s.status = 'A'
				and s.soldSeparately = 1
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
			inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID 
				and r.status = 'A' 
				and r.isRenewalRate = 0
				and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp ON srfrp.siteID = @siteID
				AND srfrp.siteResourceID = r.siteResourceID
			    AND srfrp.functionID = @FID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
				AND srfrp.rightPrintID = gprp.rightPrintID
			inner join dbo.ams_members as m on m.groupPrintID = gprp.groupPrintID
			    and m.memberID = @memberID
			left outer join dbo.sub_subscribers as sr 
				inner join dbo.sub_statuses as st on st.statusID = sr.statusID and st.statusCode = 'A'
				inner join dbo.ams_members as m2 on m2.memberID = sr.memberID
				on sr.subscriptionID = s.subscriptionID and m2.activeMemberID = @memberID
			where sr.subscriberID is null
			group by s.subscriptionID, s.typeID, s.subscriptionName, t.typeName
			ORDER BY t.typeName, s.subscriptionName;
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery dbtype="query" name="local.strReturn.qrySubscriptionTypes">
			SELECT distinct typeID, typeName
			FROM [local].strReturn.qrySubscriptions
			ORDER BY typeName
		</cfquery>
	
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getRateToUse" access="public" output="false" returntype="struct">
		<cfargument name="useRates" type="numeric" required="true">
		<cfargument name="rateAmt" type="numeric" required="true">
		<cfargument name="rateInstallments" type="numeric" required="true">
		<cfargument name="numPaymentsToUse" type="numeric" required="true">
		<cfargument name="pcPctOff" type="numeric" required="true">
		<cfargument name="pcRateAmt" type="numeric" required="true">
	
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
			
		<cfset local.strReturn.loopNumMonths = arguments.numPaymentsToUse>

		<cfif arguments.useRates is 1>
			<cfset local.strReturn.rateTotal = arguments.rateAmt * arguments.rateInstallments>
			<cfset local.strReturn.rateTotal = local.strReturn.rateTotal - (local.strReturn.rateTotal * (arguments.pcPctOff * 0.01))>
		<cfelse>
			<cfset local.strReturn.rateTotal = arguments.pcRateAmt>
		</cfif>

		<cfset local.strReturn.rateToUse = local.strReturn.rateTotal / local.strReturn.loopNumMonths>
		<cfset local.strReturn.rateLeftover = local.strReturn.rateTotal - (numberformat((local.strReturn.rateTotal / local.strReturn.loopNumMonths), "_.__") * local.strReturn.loopNumMonths)>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="updateFreeSubs" access="private" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="setUID" type="string" required="true">
		<cfargument name="setParentUID" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strReturn = { subXML=duplicate(arguments.subXML) }>

		<cfset local.setID = xmlSearch(local.strReturn.subXML,"string(//set[@uid = '#arguments.setUID#']/@id)")>
		<cfset local.setParentID = xmlSearch(local.strReturn.subXML,"string(//subscription[@uid = '#arguments.setParentUID#']/@id)")>
		
		<!--- If the set ID can't be found, it was most likely removed due to the last sub in it being removed --->
		<cfif len(local.setID) gt 0>
		
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddonData">
				select ao.addOnID, ao.useAcctCodeInSet, ao.PCnum, ao.PCPctOffEach
				from dbo.sub_addons as ao
				inner join dbo.sub_sets as sets on sets.setID = ao.childSetID
					and sets.setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.setID#">
				where ao.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.setParentID#">
			</cfquery>
			
			<cfset local.subFreeCount = XMLSearch(local.strReturn.subXML,"count(//subscription[@uid='#arguments.setParentUID#']/set[@uid='#arguments.setUID#']/subscription[@pcisfree='true'])")>
			<cfif local.subFreeCount gt local.qryAddonData.PCNum>
				<cfset local.subNewFreeNodes = XMLSearch(local.strReturn.subXML,"//subscription[@uid='#arguments.setParentUID#']/set[@uid='#arguments.setUID#']/subscription[@pcisfree='true' and @alreadysub='false']")>
				<cfset local.subAlreadyFreeNodes = XMLSearch(local.strReturn.subXML,"//subscription[@uid='#arguments.setParentUID#']/set[@uid='#arguments.setUID#']/subscription[@pcisfree='true' and @alreadysub='true']")>	
				<cfset local.numToRemove = local.subFreeCount - local.qryAddonData.PCNum>

				<!--- start with new free nodes --->
				<cfloop array="#local.subNewFreeNodes#" index="local.thisSub">
					<cfif local.numToRemove gt 0>
						<cfset local.thisSub.xmlAttributes.pcisfree = 'false'>
						<cfset local.numToRemove = local.numToRemove - 1>
					</cfif>
				</cfloop>

				<!--- then the already free nodes --->
				<cfloop array="#local.subAlreadyFreeNodes#" index="local.thisSub">
					<cfif local.numToRemove gt 0 
						AND (local.thisSub.xmlAttributes.currStatus eq 'R' OR local.thisSub.xmlAttributes.currStatus eq 'O') 
						AND (NOT (StructKeyExists(local.thisSub.xmlAttributes,"deleteme") AND local.thisSub.xmlAttributes.deleteme eq 1))>
						<cfset local.thisSub.xmlAttributes.pcisfree = 'false'>
						<cfset local.numToRemove = local.numToRemove - 1>
					</cfif>
				</cfloop>
				
			<cfelseif local.subFreeCount lt local.qryAddonData.PCNum>
				<cfset local.subNewNonFreeNodes = XMLSearch(local.strReturn.subXML,"//subscription[@uid='#arguments.setParentUID#']/set[@uid='#arguments.setUID#']/subscription[@pcisfree='false' and @alreadysub='false']")>	
				<cfset local.subAlreadyNonFreeNodes = XMLSearch(local.strReturn.subXML,"//subscription[@uid='#arguments.setParentUID#']/set[@uid='#arguments.setUID#']/subscription[@pcisfree='false' and @alreadysub='true']")>	
				<cfset local.numToAdd = local.qryAddonData.PCNum - local.subFreeCount>

				<!--- start with new non-free nodes --->
				<cfloop array="#local.subNewNonFreeNodes#" index="local.thisSub">
					<cfif local.numToAdd gt 0>
						<cfset local.thisSub.xmlAttributes.pcisfree = 'true'>
						<cfset local.numToAdd = local.numToAdd - 1>
					</cfif>
				</cfloop>

				<!--- then the already non-free nodes --->
				<cfloop array="#local.subAlreadyNonFreeNodes#" index="local.thisSub">
					<cfif local.numToAdd gt 0 
						AND (local.thisSub.xmlAttributes.currStatus eq 'R' OR local.thisSub.xmlAttributes.currStatus eq 'O') 
						AND (NOT (StructKeyExists(local.thisSub.xmlAttributes,"deleteme") AND local.thisSub.xmlAttributes.deleteme eq 1))>
						<cfset local.thisSub.xmlAttributes.pcisfree = 'true'>
						<cfset local.numToAdd = local.numToAdd - 1>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="addSubscriberToTree" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="parentSubscriberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="rateFrequencyID" type="numeric" required="true">
		<cfargument name="status" type="string" required="true">
		<cfargument name="pcFree" type="boolean" required="true">
		<cfargument name="rateAmt" type="numeric" required="true">
		<cfargument name="recordedByMemberID" type="numeric" required="true">
		<cfargument name="statsSessionID" type="numeric" required="true">
		<cfargument name="recogStartDate" type="string" required="false" default="">
		<cfargument name="recogEndDate" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.strReturn = StructNew()>

		<cftry>
			<!--- determine GL and recog dates based on RFID (mimics getRecognitionDates fn) --->
			<cfquery name="local.qryRateInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				declare @rfid int, @subscriptionID int, @allowRateGLAccountOverride bit, @subGLAccountID int, @rootSubscriberID int, 
					@parentSubscriberID int, @GLAccountID int, @rateName varchar(200), @subStartDate datetime, @subendDate datetime, 
					@status char(1);
				set @rfid = <cfqueryparam value="#arguments.rateFrequencyID#" cfsqltype="CF_SQL_INTEGER">;
				set @subscriptionID = <cfqueryparam value="#arguments.subscriptionID#" cfsqltype="CF_SQL_INTEGER">;
				set @parentSubscriberID = <cfqueryparam value="#arguments.parentSubscriberID#" cfsqltype="CF_SQL_INTEGER">;
				set @status = <cfqueryparam value="#arguments.status#" cfsqltype="CF_SQL_CHAR">;

				select @allowRateGLAccountOverride = allowRateGLAccountOverride, @subGLAccountID = GLAccountID
				from dbo.sub_subscriptions
				where subscriptionID = @subscriptionID;

				select @subStartDate = case when @status = 'A' then getdate() else s.subStartDate end, 
					@subEndDate=s.subEndDate, @rootSubscriberID = s.rootSubscriberID
				from dbo.sub_subscribers as s
				where s.subscriberID = @parentSubscriberID;

				select @GLAccountID=case @allowRateGLAccountOverride when 1 then isnull(r.glaccountID,@subGLAccountID) else @subGLAccountID end, 
					@rateName = r.rateName
				from dbo.sub_rateFrequencies as rf
				inner join dbo.sub_rates as r on r.rateID = rf.rateID
				where rf.rfid = @rfid
				and rf.status = 'A';
				
				select @GLAccountID as GLAccountID, @rateName as rateName, @subStartDate as subStartDate, @subEndDate as subEndDate;
			</cfquery>

			<cfif len(arguments.recogStartDate) and len(arguments.recogEndDate)>
				<cfset local.recogStartDate = arguments.recogStartDate>
				<cfset local.recogEndDate = arguments.recogEndDate>
			<cfelse>
				<cfset local.recogDateStruct = getRecognitionDates(useRFID=arguments.rateFrequencyID, saleTransactionDate=now(), subStartDate=local.qryRateInfo.subStartDate, subEndDate=local.qryRateInfo.subEndDate,rootsubTermFlag="")>
				<cfset local.recogStartDate = local.recogDateStruct.recogStartDate>
				<cfset local.recogEndDate = local.recogDateStruct.recogEndDate>
			</cfif>

			<cfif listFindNoCase("A,P",arguments.status)>
				<cfquery name="local.qryAssignee" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">,
						@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">

					SELECT isnull(ma.stateID,0) as stateIDForTax, isnull(ma.postalCode,'') as zipForTax
					FROM dbo.ams_members as m
					INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activememberID
					LEFT OUTER JOIN dbo.ams_memberAddresses as ma 
						INNER JOIN dbo.ams_memberAddressTags as matag on matag.orgID = @orgID and matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
						INNER JOIN dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
						ON ma.orgID = @orgID and ma.memberid = m2.memberID
					WHERE m.orgID = @orgID
					AND m.memberID = @memberID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				
				<cfset local.strTaxIndiv = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.qryRateInfo.GLAccountID, 
					saleAmount=arguments.rateAmt, transactionDate=now(), stateIDForTax=val(local.qryAssignee.stateIDForTax),
					zipForTax=local.qryAssignee.zipForTax)>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTopProfileID">
					select profileID
					from dbo.sub_rateFrequenciesMerchantProfiles
					where rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rateFrequencyID#">
					and status <> 'D'
				</cfquery>
				<cfset local.topProfileIDList = valueList(local.qryTopProfileID.profileID)>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddSub" result="local.qryAddSubResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @parentSubscriberID int, @memberID int, @subscriptionID int, @RFID int, @status char(1), 
						@pcFree bit, @recordedByMemberID int, @paidStatusID int, @siteID int, @orgID int, @GLAccountID int, 
						@allowRateGLAccountOverride bit, @activationOptionCode char(1), @rootSubscriberID int, 
						@rateGLAccountID int, @subStartDate datetime, @subEndDate datetime, @graceEndDate datetime, 
						@recogStartDate datetime, @recogEndDate datetime, @subscriberID int, @rootSubscriptionID int, 
						@rateAmt decimal(18,2), @invoiceProfileID int, @invoiceID int, @invoiceNumber varchar(19), @XMLSchedule xml,
						@statsSessionID bigint, @rateName varchar(100), @subscriptionName varchar(100), @detail varchar(max),
						@nowDate datetime, @subTransactionID int, @rc int;
					set @parentSubscriberID = <cfqueryparam value="#arguments.parentSubscriberID#" cfsqltype="CF_SQL_INTEGER">;
					set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;
					set @subscriptionID = <cfqueryparam value="#arguments.subscriptionID#" cfsqltype="CF_SQL_INTEGER">;
					set @RFID = <cfqueryparam value="#arguments.rateFrequencyID#" cfsqltype="CF_SQL_INTEGER">;
					set @status = <cfqueryparam value="#arguments.status#" cfsqltype="CF_SQL_CHAR">;
					set @pcFree = <cfqueryparam value="#arguments.pcFree#" cfsqltype="CF_SQL_BIT">;
					set @recordedByMemberID = <cfqueryparam value="#arguments.recordedByMemberID#" cfsqltype="CF_SQL_INTEGER">;
					set @rateAmt = <cfqueryparam value="#arguments.rateAmt#" cfsqltype="CF_SQL_DOUBLE">;
					set @statsSessionID = <cfqueryparam value="#arguments.statsSessionID#" cfsqltype="CF_SQL_BIGINT">;
					set @rateGLAccountID = <cfqueryparam value="#local.qryRateInfo.GLAccountID#" cfsqltype="CF_SQL_INTEGER">;
					set @recogStartDate = <cfqueryparam value="#local.recogStartDate#" cfsqltype="CF_SQL_DATE">;
					set @recogEndDate = <cfqueryparam value="#local.recogEndDate#" cfsqltype="CF_SQL_DATE">;
					set @rateName = <cfqueryparam value="#local.qryRateInfo.rateName#" cfsqltype="CF_SQL_VARCHAR">;

					select @paidStatusID=statusID from dbo.sub_paymentStatuses where statusCode = 'P';
					select @nowDate = getdate();

					select @siteID=sites.siteID, @orgID=sites.orgID, @GLAccountID=sub.GLAccountID, @subscriptionName=sub.subscriptionName, 
						@allowRateGLAccountOverride=sub.allowRateGLAccountOverride, @activationOptionCode=ao.subActivationCode
					from dbo.sub_subscriptions as sub
					inner join dbo.sub_types as st on st.typeID = sub.typeID
					inner join dbo.sites on sites.siteID = st.siteID
					inner join dbo.sub_activationOptions as ao on ao.subActivationID = sub.subActivationID
					where sub.subscriptionID = @subscriptionID
					and sub.status = 'A';

					select @rootSubscriberID=s.rootSubscriberID, @rootSubscriptionID=sroot.subscriptionID, 
						@subStartDate = case when @status = 'A' then getdate() else s.subStartDate end, 
						@subEndDate=s.subEndDate, @graceEndDate=s.graceEndDate
					from dbo.sub_subscribers as s
					inner join dbo.sub_subscribers as sRoot on sRoot.subscriberID = s.rootSubscriberID
					where s.subscriberID = @parentSubscriberID;

					IF @allowRateGLAccountOverride = 1 AND @rateGLAccountID is not null
						select @GLAccountID = @rateGLAccountID;

					<cfif len(arguments.recogStartDate) and len(arguments.recogEndDate)>
						set @recogStartDate = <cfqueryparam value="#dateFormat(arguments.recogStartDate,'m/d/yyyy')#" cfsqltype="CF_SQL_DATE">;
						set @recogEndDate = <cfqueryparam value="#dateFormat(arguments.recogEndDate,'m/d/yyyy')# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">;
					<cfelse>
						-- if blank, default to sub dates
						IF @recogStartDate is null 	
							set @recogStartDate = @subStartDate;
						IF @recogEndDate is null 	
							set @recogEndDate = @subEndDate;

						-- recognition end date cannot be after sub end date
						IF @recogEndDate > @subEndDate
							set @recogEndDate = @subEndDate;
					</cfif>

					-- recognition start date must be before recognition end date
					IF @recogStartDate > @recogEndDate
						set @recogEndDate = @recogStartDate;

	
					<!--- dont add if the member already has this subscription in the tree --->
					IF EXISTS (	select 1 
								from dbo.sub_subscribers as s
								inner join dbo.sub_statuses as st on st.statusID = s.statusID 
								where s.rootSubscriberID = @rootSubscriberID 
								and s.subscriptionID = @subscriptionID
								and st.statusCode not in ('D','X'))				
						RAISERROR('Subscription already in the subscription tree.',16,1);


					BEGIN TRAN;
						EXEC dbo.sub_addSubscriber @orgID=@orgID, @memberID=@memberID, @subscriptionID=@subscriptionID,
							@parentSubscriberID=@parentSubscriberID, @RFID=@RFID, @GLAccountID=@GLAccountID, @status=@status, 
							@subStartDate=@subStartDate, @subEndDate=@subEndDate, @graceEndDate=@graceEndDate, 
							@recogStartDate=@recogStartDate, @recogEndDate=@recogEndDate, @pcfree=@pcFree, 
							@activationOptionCode=@activationOptionCode, @recordedByMemberID=@recordedByMemberID, 
							@bypassQueue=1, @subscriberID=@subscriberID OUTPUT;
						IF @subscriberID = 0 RAISERROR('Unable to add subscriber.',16,1);

						update dbo.sub_subscribers
						set lastPrice = @rateAmt
						where subscriberID = @subscriberID;

						IF @status = 'A' BEGIN
							EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID, @newStatusCode='P', @siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @bypassQueue=1, @result=@rc OUTPUT;
							IF @rc = 1
								EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID, @newStatusCode='A', @siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @bypassQueue=1, @result=@rc OUTPUT;
						END
						IF @status = 'P' BEGIN
							EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='P', @siteID=@siteID, @enteredByMemberID=@recordedByMemberID, @bypassQueue=1, @result=@rc OUTPUT;
						END

						EXEC dbo.sub_suppressSubscriberAutomatedEmail @subscriberID=@subscriberID, @enteredByMemberID=@recordedByMemberID, @limitToEmailTemplateID=NULL;
						EXEC dbo.sub_fixSubscriberTreeOrder @rootSubscriberID=@rootSubscriberID;

						<cfif listFindNoCase("A,P",arguments.status)>
							IF @status in ('A','P') BEGIN
								select @invoiceProfileID = invoiceProfileID
								from dbo.tr_GLAccounts
								where glAccountID = @GLAccountID;

								EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
										@assignedToMemberID=@memberID, @dateBilled='#dateformat(now(),"m/d/yyyy")#', @dateDue='#dateformat(now(),"m/d/yyyy")#', 
										@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList='#local.topProfileIDList#';

								set @detail = left(@subscriptionName + ' - ' + @rateName,500);
								set @XMLSchedule = '<rows><row amt="' + cast(@rateAmt as varchar(10)) + '" dt="' + convert(varchar(10),@recogStartDate,101) + '" /></rows>';

								EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@memberID, 
									@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', @detail=@detail, 
									@amount=@rateAmt, @transactionDate=@nowDate, @creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @parentTransactionID=null, 
									@stateIDForTax=<cfif val(local.qryAssignee.stateIDForTax)>#local.qryAssignee.stateIDForTax#<cfelse>null</cfif>, 
									@zipForTax=<cfif len(local.qryAssignee.zipForTax)>'#local.qryAssignee.zipForTax#'<cfelse>null</cfif>, 
									@taxAmount=#val(local.strTaxIndiv.totalTaxAmt)#, @xmlSchedule=@XMLSchedule, @transactionID=@subTransactionID OUTPUT;

								EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Admin', @transactionID=@subTransactionID, 
									@itemType='Dues', @itemID=@subscriberID, @subItemID=null;

								EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;
							END
						</cfif>
					COMMIT TRAN;

					IF @status in ('A','P') BEGIN
						EXEC dbo.sub_checkActivationsByMember @orgID=@orgID, @memberid=@memberID, @subscriberID=@subscriberID, @bypassQueue=0;
					END

					-- return query
					select @subscriberID as subscriberID, @invoiceID as invoiceID, @invoiceProfileID as invoiceProfileID, @rateAmt as invoiceAmount;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.strReturn.subscriberID = local.qryAddSub.subscriberID>
			<cfset local.strReturn.invoiceID = local.qryAddSub.invoiceID>
			<cfset local.strReturn.invoiceProfileID = local.qryAddSub.invoiceProfileID>
			<cfset local.strReturn.invoiceAmount = local.qryAddSub.invoiceAmount>

		<cfcatch type="Any">
			<cfset local.strReturn.subscriberID = 0>
			<cfset local.strReturn.invoiceID = 0>
			<cfset local.strReturn.invoiceProfileID = 0>
			<cfset local.strReturn.invoiceAmount = 0>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="autoSubscribe" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subStruct" type="struct" required="true">
		<cfargument name="invoiceMonths" type="numeric" required="false" default="0">
		<cfargument name="newAsBilled" type="boolean" required="false" default="false">
		<cfargument name="newAsRenewed" type="boolean" required="false" default="false">
		<cfargument name="isRenewal" type="boolean" required="false" default="false">
		<cfargument name="startDateOverride" type="string" required="false" default="">
		<cfargument name="saleDateOverride" type="string" required="false" default="">
		<cfargument name="offerExpireDate" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cftry>
			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.errReason = "">
			<cfset local.returnStruct.rootSubscriberID = 0>
			<cfset local.maxFrequencyInstallments = getMaxFrequencyInstallments(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
	
			<cfif structKeyExists(arguments.subStruct,"uid")>
				<cfset local.actorMemberID = session.cfcuser.memberdata.memberID GT 0 ? session.cfcuser.memberdata.memberID : arguments.memberID>
				<cfset removeSubXML(actorMemberID=local.actorMemberID, memberID=arguments.memberID)>
				<cfset local.xmlSubscribeMember = loadSubXML(actorMemberID=local.actorMemberID, memberID=arguments.memberID)>
				<cfset local.xmlSubscribeMember.xmlRoot.process.xmlAttributes["useRenewalRates"] = false/>
				<cfset local.puid = xmlSearch(local.xmlSubscribeMember,"string(//process/@pointer)")>
				<cfset local.pointerNode = xmlSearch(local.xmlSubscribeMember,"//node()[@uid = '#local.puid#']")>
				
				<!--- add root --->
				<cfset local.qryRootSubName = getSubscriptionInfoByUID(subscriptionUID=arguments.subStruct.uid)>
				
				<cfif local.qryRootSubName.recordcount is 0>
					<cfset local.returnStruct.success = false>
					<cfset local.returnStruct.errReason = "Invalid Subscription">
				<cfelse>
					<cfset local.strAddSubResult = addSub(subXML=local.xmlSubscribeMember, parentUID=local.puid, subid=local.qryRootSubName.subscriptionID, 
							subname=local.qryRootSubName.subscriptionName, subTermFlag=local.qryRootSubName.rateTermDateFlag, GLAccountIDToUse=local.qryRootSubName.GLAccountID, 
							allowRateGLOverride=local.qryRootSubName.allowRateGLAccountOverride, payOrder=local.qryRootSubName.subPayOrder,
							activationOptionCode=local.qryRootSubName.subActivationCode, alternateActivationOptionCode=local.qryRootSubName.subAlternateActivationCode)>
	
					<cfset local.subRootUID = local.strAddSubResult.subUID>
					<cfif len(local.subRootUID)>
						<cfset local.xmlSubscribeMember = local.strAddSubResult.subXML>
						<cfset local.rootRateUID = ''>
						<cfset local.rootOverrideRatePerms = false>
						<cfif structKeyExists(arguments.subStruct, "rateUID")>		
							<cfset local.rootRateUID = arguments.subStruct.rateUID>
						</cfif>
						<cfif structKeyExists(arguments.subStruct, "overridePerms")>		
							<cfset local.rootOverrideRatePerms = arguments.subStruct.overridePerms>
						</cfif>
						<cfset local.qryRate = getRates(
							siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
							memberID=arguments.memberID, 
							subscriptionID=local.qryRootSubName.subscriptionID,
							isRenewal=arguments.isRenewal,
							rateUID=local.rootRateUID,
							overridePerms=local.rootOverrideRatePerms)>
						
						<cfif local.qryRate.recordCount neq 1>
							<cfif structKeyExists(arguments.subStruct, "freqUID")>
								<cfset local.freqUIDToUse = arguments.substruct.freqUID>
							<cfelse>
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFullFreq">
									select UPPER(uid) as uid
									from dbo.sub_frequencies
									where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
									and frequencyShortName = 'F' and status = 'A'
								</cfquery>
	
								<cfset local.freqUIDToUse = local.qryFullFreq.uid>
							</cfif>
							
							<cfquery dbtype="query" name="local.qryRateFreq">
								select rfid
								from [local].qryRate
								where frequencyUID = '#local.freqUIDToUse#'
							</cfquery>
							
							<cfif local.qryRateFreq.recordCount neq 1>
								<cfset local.returnStruct.success = false>
								<cfset local.returnStruct.errReason = "Invalid Rate for subscription: #local.qryRootSubName.subscriptionName# (#local.subRootUID#)">
							<cfelse>
								<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.subRootUID, rfid=local.qryRateFreq.rfid)>
								<cfset local.xmlSubscribeMember = local.strAddRateResult.subXML>
							</cfif>
						<cfelse>
							<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFreqFromRFID">
								select UPPER(f.uid) as uid
								from dbo.sub_frequencies f
								inner join dbo.sub_rateFrequencies rf
									on rf.frequencyID = f.frequencyID
									and rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRate.rfid#">
								where f.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
							</cfquery>
							<cfset local.freqUIDToUse = local.qryFreqFromRFID.uid>
							<cfset local.strAddRateResult = addRate(subXML=local.xmlSubscribeMember, parentUID=local.subRootUID, rfid=local.qryRate.rfid)>
							<cfset local.xmlSubscribeMember = local.strAddRateResult.subXML>
						</cfif>
						<cfif structKeyExists(arguments.subStruct, "rateOverride")>
							<cfset local.rootSubNodes = XMLSearch(local.xmlSubscribeMember,"//subscription[@uid='#local.subRootUID#']")>
							<cfif arrayLen(local.rootSubNodes)>
								<cfset local.rootSub = local.rootSubNodes[1]>
								<cfset local.rootSub.xmlAttributes.userates = 0>
								<cfset local.rootSub.xmlAttributes.pricechanged = 1>
								<cfset local.rootSub.xmlAttributes.pcrateamt = arguments.subStruct.rateOverride>
							</cfif>
						</cfif>
					<cfelse>
						<cfset local.returnStruct.success = false>
						<cfset local.returnStruct.errReason = "Unable to create subscription (#arguments.subStruct.uid#)">
					</cfif>
				</cfif>
				
				
				<cfif local.returnStruct.success AND structKeyExists(arguments.subStruct, "children")>
					<cfset local.addChildrenResult = autoAddChildSubs(subXML=local.xmlSubscribeMember, siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=arguments.memberID, parentSubUID=arguments.subStruct.uid, parentXMLUID=local.subRootUID, parentFreqUID=local.freqUIDToUse,
						arrChildren=arguments.subStruct.children, isRenewal=arguments.isRenewal)>

					<cfset local.xmlSubscribeMember = local.addChildrenResult.subXML>
	
					<cfif local.addChildrenResult.success eq false>
						<cfset local.returnStruct.success = false>
						<cfset local.returnStruct.errReason = local.addChildrenResult.errReason>
					</cfif>
					
				</cfif>
				
				<cfif local.returnStruct.success>
					<cfset saveSubXML(actorMemberID=local.actorMemberID, memberID=arguments.memberID, subXML=toString(local.xmlSubscribeMember))>
					
					<cfset arguments.event.setValue("mid", arguments.memberID)>
	
					<cfset local.arrPaySchedule = arrayNew(1)>
					<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
						<cfset local.strPS = { date='', amount='' }>
						<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
					</cfloop>
	
					<cfset local.amountToCharge = 0>
					<cfset local.firstPaymentMinimum = 0>
					<cfset local.numPaymentsToUse = 0>
					<cfset local.rootFreqID = 0>
					
					<cfif arguments.invoiceMonths neq 0>
						<cfset local.numPaymentsToUse = arguments.invoiceMonths>
					<cfelse>
						<cfset local.termDateRFID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rfid)")>
						<cfset local.subTermFlag = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@termflag)")>
						<cfset local.rootNumPayments = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rateInstallments)")>
						<cfset local.rootRateInterval = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@rateInterval)")>
						<cfset local.rootFreqID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@freqid)")>
						
						<cfset local.subTermDates = getSubTermDates(termDateRFID=local.termDateRFID, subTermFlag=local.subTermFlag)>
						<cfif local.subTermDates.success eq true>
							<cfset local.subPaymentDates = getSubPaymentDates(local.subTermDates.subTermStartDate, local.subTermDates.subTermEndDate, local.rootNumPayments, local.rootRateInterval)>
							<cfset local.numPaymentsToUse = local.subPaymentDates.numPayments>
						<cfelse>
							<cfset local.returnStruct.success = false>
							<cfset local.returnStruct.errReason = "Unable to determine subscription dates">
						</cfif>
						
					</cfif>
					
					<cfif local.returnStruct.success>
						<cfset local.subAmts = getSubscriptionAmounts(subXML=local.xmlSubscribeMember, numPaymentsToUse=local.numPaymentsToUse, invoiceMonths=arguments.invoiceMonths)>
	
						<cfset local.amountToCharge = local.subAmts.qryTotals.totalAmt>
	
						<cfif local.subAmts.qryUpFrontAmt.totalAmt gt 0>
							<cfset local.arrPaySchedule[1] = { date=now(), amount=numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__") }>
							<cfset local.firstPaymentMinimum = numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__")>
						</cfif>
	
						<cfset local.distribAmt = local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse>
						<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.subAmts.qryNonUpFrontAmt.totalAmt - (numberformat((local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

						<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
							<cfset local.loopAmt = 0>
							<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
							<cfset local.arrPaySchedule[local.loopCnt] = { date=now(), amount=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
						</cfloop>
						<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >
						
						<cfif arguments.invoiceMonths neq 0>
							<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
								<cfif len(local.arrPaySchedule[local.thisP].amount) gt 0>
									<cfset local.arrPaySchedule[local.thisP].date = dateAdd("m", (local.thisP-1), now())>
								</cfif>
							</cfloop>
						<cfelse>
							<cfloop from="1" to="#ArrayLen(local.subPaymentDates.arrDates)#" index="local.thisP">
								<cfset local.arrPaySchedule[local.thisP].date = local.subPaymentDates.arrDates[local.thisP]>
							</cfloop>
						</cfif>
						
						<cfset local.payOrderIndex = 1>
						<!--- Invoices --->
						<cfset arguments.event.setValue("hInvoiceProfileIDs", valueList(local.subAmts.qryInvProfiles.invoiceProfileID))>
						
						<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopIndex">
	
							<cfset local.isPayOrderIndexInitialized = false>
							<cfset local.thisInvAmt = local.arrPaySchedule[local.loopIndex].amount>
	
							<cfset arguments.event.setValue("ps_#local.loopIndex#_date", "#Month(local.arrPaySchedule[local.loopIndex].date)#/#Day(local.arrPaySchedule[local.loopIndex].date)#/#Year(local.arrPaySchedule[local.loopIndex].date)#")>
							<cfset arguments.event.setValue("ps_#local.loopIndex#_amt", "#local.thisInvAmt#")>
							<cfset arguments.event.setValue("ps_#local.loopIndex#_subscriptionWithSalesNeeded", "")>
							
							<cfset local.strProfile = structNew()>
							
							<cfset local.invoiceAmtApplied = 0>
							<cfloop condition="(local.thisInvAmt-local.invoiceAmtApplied) gt 0.0 and local.payOrderIndex lte arrayLen(local.subAmts.payOrderArray)">

								<cfif not local.isPayOrderIndexInitialized>
									<!--- All invoice profiles with any subscriptions set to current payorder will be affected by current payment --->
									<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
										<cfset arguments.event.setValue("hps_#local.loopIndex#_#local.thisProfileEntry.id#_appearOnThisInvoice", "true")>
									</cfloop>
									<cfset local.isPayOrderIndexInitialized = true />
								</cfif>									
				
								<cfset local.currAmount = (local.subAmts.payOrderArray[local.payOrderIndex].total - local.subAmts.payOrderArray[local.payOrderIndex].totalapplied)>
				
								<cfif ((local.thisInvAmt-local.invoiceAmtApplied) gte local.currAmount)>
									<cfset local.invoiceAmtApplied = local.invoiceAmtApplied + local.currAmount>
									<cfset local.subAmts.payOrderArray[local.payOrderIndex].totalapplied = local.subAmts.payOrderArray[local.payOrderIndex].total>
				
									<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
										<cfset local.innerCurrAmount = (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied)>
										<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.total>
										<cfif not StructKeyExists(local.strProfile, "#local.thisProfileEntry.id#")>
											<cfset local.strProfile["#local.thisProfileEntry.id#"] = 0>
										</cfif>
										<cfset local.strProfile["#local.thisProfileEntry.id#"] = local.strProfile["#local.thisProfileEntry.id#"] + local.innerCurrAmount>
									</cfloop>
								<cfelseif ((local.thisInvAmt-local.invoiceAmtApplied) lt local.currAmount)>
									<cfset local.pctToUse = ((local.thisInvAmt-local.invoiceAmtApplied) / local.currAmount)>
									<cfset local.leftOver = numberformat((local.thisInvAmt-local.invoiceAmtApplied) - numberformat((((local.thisInvAmt-local.invoiceAmtApplied) / local.currAmount) * local.currAmount), "_.__"), "_.__")>
													
									<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
										<cfset local.innerCurrAmount = numberformat((local.thisProfileEntry.total - local.thisProfileEntry.totalapplied) * local.pctToUse, "_.__")>
										<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.totalapplied + local.innerCurrAmount>
											
										<cfif ((local.leftOver gt 0) AND (local.thisProfileEntry.totalapplied lt local.thisProfileEntry.total))>
											<cfif (local.leftOver gt (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
												<cfset local.innerCurrAmount = local.innerCurrAmount + (local.leftOver - (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
												<cfset local.leftOver = local.leftOver - ((local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
											<cfelse>
												<cfset local.innerCurrAmount += local.leftOver>
												<cfset local.leftOver = 0>
											</cfif>
											<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.totalapplied + local.innerCurrAmount>
										</cfif>
											
										<cfif not StructKeyExists(local.strProfile, "#local.thisProfileEntry.id#")>
											<cfset local.strProfile["#local.thisProfileEntry.id#"] = 0>
										</cfif>
										<cfset local.strProfile["#local.thisProfileEntry.id#"] = local.strProfile["#local.thisProfileEntry.id#"] + local.innerCurrAmount>
									</cfloop>
									
									<cfset local.subAmts.payOrderArray[local.payOrderIndex].totalapplied = numberformat(local.subAmts.payOrderArray[local.payOrderIndex].totalapplied + local.thisInvAmt-local.invoiceAmtApplied, "_.__")>
									<cfset local.invoiceAmtApplied = local.thisInvAmt>
								</cfif>
				
								<cfif ((local.subAmts.payOrderArray[local.payOrderIndex].total-local.subAmts.payOrderArray[local.payOrderIndex].totalapplied) lte 0.0)>
									<cfset local.payOrderIndex = local.payOrderIndex + 1>
									<cfset local.isPayOrderIndexInitialized = false>
								</cfif>
							
							</cfloop>
	
							<cfloop array="#StructKeyArray(local.strProfile)#" index="local.thisProfileKey">
								<cfset local.loopCurrAmt = local.strProfile["#local.thisProfileKey#"]>
								<cfset arguments.event.setValue("hps_#local.loopIndex#_#local.thisProfileKey#_amt", "#numberformat(local.loopCurrAmt, "_.__")#")>
							</cfloop>

						</cfloop>
						
						<cfif arguments.newAsBilled>
							<cfset arguments.event.setValue("chkBilled", 1)>
						</cfif>
						<cfif arguments.newAsRenewed>
							<cfset arguments.event.setValue("chkRenewed", 1)>
						</cfif>
						<cfif len(arguments.startDateOverride) gt 0>
							<cfset arguments.event.setValue("startDateOverride", arguments.startDateOverride)>
						</cfif>
						<cfif len(arguments.saleDateOverride) gt 0>
							<cfset arguments.event.setValue("saleDateOverride", arguments.saleDateOverride)>
						</cfif>
						<cfset local.arguments = duplicate(arguments)>
						<cfset structDelete(local.arguments,"event")>
						<cfset local.arguments.event = arguments.event.getCollection()>
						<!--- doConfirm --->
						<cfset local.confirmRetStruct = doConfirm(subXML=local.xmlSubscribeMember, strEventCollection=arguments.event.getCollection())>
						<cfif local.confirmRetStruct.success eq 0>
							<cfset local.returnStruct.success = false>
							<cfset local.returnStruct.errReason = "Unable to add subscription">
						<cfelse>
							<cfset local.returnStruct.rootSubscriberID = local.confirmRetStruct.topParentSubscriberID>
							<cfif len(arguments.offerExpireDate) and isDate(arguments.offerExpireDate) and (arguments.newAsBilled OR arguments.newAsRenewed)>
								<cfset local.updateRecindDateSuccess = createObject("component","model.admin.subscriptions.subscriptions").updateOfferExpireDate(subscriberID = local.returnStruct.rootSubscriberID, siteID = arguments.event.getValue('mc_siteinfo.siteid'), offerExpireDate = arguments.offerExpireDate)>
							</cfif>
						</cfif>
					</cfif>			
				</cfif>
			<cfelse>			
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errReason = "Invalid arguments">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="model.admin.subscriptions.subscriptionReg.autoSubscribe Wrapper")>
			<cfrethrow>
		</cfcatch>
		</cftry>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="autoAddChildSubs" access="private" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="parentSubUID" type="string" required="true">
		<cfargument name="parentXMLUID" type="string" required="true">
		<cfargument name="parentFreqUID" type="string" required="true">
		<cfargument name="arrChildren" type="any" required="true">
		<cfargument name="isRenewal" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "subXML":duplicate(arguments.subXML), "errReason":"" }>
		
		<cfloop array="#arguments.arrChildren#" index="local.thisSubEntry">
			
			<cfif local.returnStruct.success>
				<cfset local.qrySubName = getChildSubscriptionInfoByUID(parentSubscriptionUID=arguments.parentSubUID,childSubscriptionUID=local.thisSubEntry.uid)>
				<cfif local.qrySubName.recordCount neq 1>
					<cfset local.returnStruct.success = false>
					<cfset local.returnStruct.errReason = "Unable to get child subscription (#local.thisSubEntry.uid#)">
				<cfelse>
					<cfset local.strAddSetResult = addSet(subXML=local.returnStruct.subXML, parentUID=arguments.parentXMLUID, setid=local.qrySubName.setid, setname=local.qrySubName.setname)>
					<cfif len(local.strAddSetResult.setUID)>
						<cfset local.returnStruct.subXML = local.strAddSetResult.subXML>
						<!--- get the number of free subs added to determine if this one is free --->
						<cfset local.subFreeCount = XMLSearch(local.returnStruct.subXML,"count(//subscription[@uid='#arguments.parentXMLUID#']/set[@uid='#local.strAddSetResult.setUID#']/subscription[@pcisfree='true'])")>	

						<cfif local.qrySubName.PCNum gt local.subFreeCount>
							<cfset local.currSubIsFree = true>
						<cfelse>
							<cfset local.currSubIsFree = false>
						</cfif>

						<cfif local.qrySubName.useAcctCodeInSet eq 0>
							<!--- use the parent GLAccountID --->
							<cfset local.currGLAToUse = val(xmlSearch(local.xmlSubscribeMember,"string(//set[@id=0]/subscription/@glaccountidtouse)"))>
						<cfelse>
							<cfset local.currGLAToUse = local.qrySubName.GLAccountID>
						</cfif>

						<cfset local.strAddSubResult = addSub(subXML=local.returnStruct.subXML, parentUID=local.strAddSetResult.setUID, subid=local.qrySubName.subscriptionID, 
								subname=local.qrySubName.subscriptionName, subTermFlag=local.qrySubName.rateTermDateFlag, GLAccountIDToUse=local.currGLAToUse, 
								allowRateGLOverride=local.qrySubName.allowRateGLAccountOverride, pcnumfree=local.qrySubName.PCnum, pcpctoff=local.qrySubName.PCPctOffEach, 
								pcfree=local.currSubIsFree,  payOrder=local.qrySubName.subPayOrder, activationOptionCode=local.qrySubName.subActivationCode,
								alternateActivationOptionCode=local.qrySubName.subAlternateActivationCode)>
						<cfif len(local.strAddSubResult.subUID)>
							<cfset local.strUpdateFreeSubsResult = updateFreeSubs(subXML=local.strAddSubResult.subXML, setUID=local.strAddSetResult.setUID, setParentUID=arguments.parentXMLUID)>
							<cfset local.returnStruct.subXML = local.strUpdateFreeSubsResult.subXML>
							
							<cfset local.childRateUID = ''>
							<cfset local.childOverrideRatePerms = false>
							<cfif structKeyExists(local.thisSubEntry, "rateUID")>		
								<cfset local.childRateUID = local.thisSubEntry.rateUID>
							</cfif>
							<cfif structKeyExists(local.thisSubEntry, "overridePerms")>		
								<cfset local.childOverrideRatePerms = local.thisSubEntry.overridePerms>
							</cfif>
							
							<cfset local.qryRate = getRates(siteID=arguments.siteID, memberID=arguments.memberID, subscriptionID=local.qrySubName.subscriptionID, 
													isRenewal=arguments.isRenewal, rateUID=local.childRateUID, overridePerms=local.childOverrideRatePerms)>
							<cfif local.qryRate.recordCount neq 1>
							
								<cfquery dbtype="query" name="local.qryRateParentFreq">
									select rfid
									from [local].qryRate
									where frequencyUID = '#arguments.parentFreqUID#'
								</cfquery>

								<cfif local.qryRateParentFreq.recordCount neq 1>
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFullFreq">
										select UPPER(uid) as uid
										from dbo.sub_frequencies
										where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
										and frequencyShortName = 'F'
									</cfquery>

									<cfquery dbtype="query" name="local.qryRateFullFreq">
										select rfid
										from [local].qryRate
										where frequencyUID = '#local.qryFullFreq.uid#'
									</cfquery>
									
									<cfif local.qryRateFullFreq.recordCount neq 1>
										<cfset local.returnStruct.success = false>
										<cfset local.returnStruct.errReason = "Invalid Rate for child subscription (#local.thisSubEntry.uid#)">
									<cfelse>
										<cfset local.strAddRateResult = addRate(subXML=local.returnStruct.subXML, parentUID=local.strAddSubResult.subUID, rfid=local.qryRateFullFreq.rfid)>
										<cfset local.returnStruct.subXML = local.strAddRateResult.subXML>
									</cfif>
								<cfelse>
									<cfset local.strAddRateResult = addRate(subXML=local.returnStruct.subXML, parentUID=local.strAddSubResult.subUID, rfid=local.qryRateParentFreq.rfid)>
									<cfset local.returnStruct.subXML = local.strAddRateResult.subXML>
								</cfif>
							<cfelse>
								<cfset local.strAddRateResult = addRate(subXML=local.returnStruct.subXML, parentUID=local.strAddSubResult.subUID, rfid=local.qryRate.rfid)>
								<cfset local.returnStruct.subXML = local.strAddRateResult.subXML>
							</cfif>
							
							<cfif structKeyExists(local.thisSubEntry, "rateOverride")>
								<cfset local.nodeSubNodes = XMLSearch(local.returnStruct.subXML,"//subscription[@uid='#local.strAddSubResult.subUID#']")>
								<cfif arrayLen(local.nodeSubNodes)>
									<cfset local.nodeSub = local.nodeSubNodes[1]>
									<cfset local.nodeSub.xmlAttributes.userates = 0>
									<cfset local.nodeSub.xmlAttributes.pricechanged = 1>
									<cfset local.nodeSub.xmlAttributes.pcrateamt = local.thisSubEntry.rateOverride>
								</cfif>
							</cfif>

							<cfif local.returnStruct.success AND structKeyExists(local.thisSubEntry, "children")>
								<cfset local.addChildrenResult = autoAddChildSubs(subXML=local.returnStruct.subXML, siteID=arguments.siteID, memberID=arguments.memberID,
																	parentSubUID=local.thisSubEntry.uid, parentXMLUID=local.strAddSubResult.subUID, parentFreqUID=arguments.parentFreqUID, 
																	arrChildren=local.thisSubEntry.children, isRenewal=arguments.isRenewal)>
								<cfset local.returnStruct.subXML = local.addChildrenResult.subXML>
								<cfif local.addChildrenResult.success eq false>
									<cfset local.returnStruct.success = false>
									<cfset local.returnStruct.errReason = local.addChildrenResult.errReason>
								</cfif>
							</cfif>
							
						<cfelse>
							<cfset local.returnStruct.success = false>
							<cfset local.returnStruct.errReason = "Unable to add child subscription (#local.thisSubEntry.uid#)">
						</cfif>
						
					<cfelse>
						<cfset local.returnStruct.success = false>
						<cfset local.returnStruct.errReason = "Unable to add child set (#local.thisSubEntry.uid#)">
					</cfif>
				</cfif>
			</cfif>			
			
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getSubscriptionInfoByUID" access="private" output="false" returntype="query">
		<cfargument name="subscriptionUID" type="string" required="true">

		<cfset var qrySub = "">

		<cfquery name="qrySub" datasource="#application.dsn.membercentral.dsn#">
			select top 1 s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, s.paymentOrder as subPayOrder, 
			o.subActivationCode, o2.subActivationCode as subAlternateActivationCode, s.allowRateGLAccountOverride
			from dbo.sub_subscriptions s
			inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
			inner join dbo.sub_activationOptions o2 on o2.subActivationID = s.subAlternateActivationID
			where s.uid = <cfqueryparam value="#arguments.subscriptionUID#" cfsqltype="CF_SQL_VARCHAR">
			and s.soldSeparately = 1
			and s.status = 'A'
		</cfquery>
			
		<cfreturn qrySub>
	</cffunction>
	
	<cffunction name="getChildSubscriptionInfoByUID" access="private" output="false" returntype="query">
		<cfargument name="parentSubscriptionUID" type="string" required="true">
		<cfargument name="childSubscriptionUID" type="string" required="true">
		
		<cfset var qrySub = "">

		<cfquery name="qrySub" datasource="#application.dsn.membercentral.dsn#">
			select ao.addOnID, s.subscriptionID, s.subscriptionName, s.rateTermDateFlag, s.GLAccountID, o.subActivationCode, 
				o2.subActivationCode as subAlternateActivationCode, sets.setid, sets.setName, ao.useAcctCodeInSet, 
				ao.PCnum, ao.PCPctOffEach, s.paymentOrder as subPayOrder, s.allowRateGLAccountOverride
			from dbo.sub_addons as ao
			inner join dbo.sub_subscriptions ps on ao.subscriptionID = ps.subscriptionID
				and ps.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.parentSubscriptionUID#">
			inner join dbo.sub_sets as sets on sets.setID = ao.childSetID
			INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
			INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID 
				and s.status = 'A'
				and s.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.childSubscriptionUID#">
			inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
			inner join dbo.sub_activationOptions o2 on o2.subActivationID = s.subAlternateActivationID
		</cfquery>
			
		<cfreturn qrySub>
	</cffunction>

	<cffunction name="autoAcceptSubscription" access="public" output="false" returntype="struct" hint="Accept Grid action and subMarkAccepted queue use this. Assumes the recordedMemberID is in session.cfcuser.memberdata.memberID">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="skipEmailTemplateNotifications" type="boolean" required="true">
		<cfargument name="bypassQueue" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = {success = false}>
		<cfset local.returnStruct.retCreate = autoCreateInvoices(orgID=arguments.orgID, siteID=arguments.siteID, siteCode=arguments.sitecode, memberID=arguments.memberID, rootSubscriberID=arguments.rootSubscriberID, bypassQueue=arguments.bypassQueue)>
		<cfset local.recordedByMemberID = session.cfcuser.memberdata.memberID>

		<cfif local.returnStruct.retCreate.success eq 0>
			<cfset local.returnStruct.success = false>
		<cfelse>

			<cfif arguments.skipEmailTemplateNotifications>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getSubscriberIDsToSkipNotfication">
					select subscriberID
					from sub_subscribers
					where rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">
				</cfquery>
			</cfif>
			<!--- change status --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateSub">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					declare @trash int, @rootSubscriberID int, @subStartDate datetime, @statusCode char(1);
					set @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">;
				
					select @subStartDate = subStartDate
					from dbo.sub_subscribers
					where subscriberID = @rootSubscriberID;

					<!--- If start date in future, status = 'P', else status = 'A' --->
					if DATEDIFF(DAY,@subStartDate,getdate()) >= 0
						set @statusCode='A';
					else 
						set @statusCode='P';

					<cfif structKeyExists(local,"getSubscriberIDsToSkipNotfication")>
						<cfloop query="local.getSubscriberIDsToSkipNotfication">
							EXEC dbo.sub_suppressSubscriberAutomatedEmail @subscriberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.getSubscriberIDsToSkipNotfication.subscriberID#">, @enteredByMemberID=#local.recordedByMemberID#, @limitToEmailTemplateID=NULL;
						</cfloop>
					</cfif>

					EXEC dbo.sub_updateSubscriberStatus @subscriberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">,
						@newStatusCode=@statusCode, @siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">,
						@bypassQueue=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.bypassQueue#">,
						@result=@trash OUTPUT;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.success = true>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="autoCreateInvoices" access="private" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="bypassQueue" type="boolean" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errcode = "">
		<cfset local.returnStruct.errReason = "">
		<cfset local.maxFrequencyInstallments = getMaxFrequencyInstallments(siteID=arguments.siteid)>

		<cfset local.strEventCollection = { mid=arguments.memberID, sid=arguments.rootSubscriberID, invOnly=true }>
		<cfset local.strEventCollection.mc_siteInfo.siteID = arguments.siteID>
		<cfset local.strEventCollection.mc_siteInfo.orgID = arguments.orgID>
		<cfset local.strEventCollection.mc_siteInfo.siteCode = arguments.siteCode>
		<cfset local.strEventCollection.mc_siteInfo.supportProviderEmail = application.objSiteInfo.getSiteInfo(arguments.siteCode).supportProviderEmail>
		<cfset local.strEventCollection.mc_siteInfo.orgname = application.objSiteInfo.getSiteInfo(arguments.siteCode).orgname>
		<cfset local.strEventCollection.mc_siteInfo.sitename = application.objSiteInfo.getSiteInfo(arguments.siteCode).sitename>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubDates">
			select subStartDate, subEndDate, graceEndDate
			from dbo.sub_subscribers
			where subscriberID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rootSubscriberID#">
		</cfquery>
		<cfset local.subStartDate = local.qrySubDates.subStartDate>
		<cfset local.subEndDate = local.qrySubDates.subEndDate>
		<cfset local.graceEndDate = local.qrySubDates.graceEndDate>

		<cfset local.reLoad = loadXMLForEdit(memberID=arguments.memberID, siteID=arguments.siteID, rootSubID=arguments.rootSubscriberID)>
		<cfset local.xmlSubscribeMember = local.reLoad.subXML>
		
		<cfset local.arrPaySchedule = arrayNew(1)>
		<cfset local.strPS = { date='', amount='' }>
		<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
			<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
		</cfloop>

		<cfset local.amountToCharge = 0>
		<cfset local.firstPaymentMinimum = 0>
		<cfset local.numPaymentsToUse = 0>
		<cfset local.rootFreqID = 0>
		
		<cfset local.termDateRFID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rfid)")>
		<cfset local.subTermFlag = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@termflag)")>
		<cfset local.rootNumPayments = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@rateInstallments)")>
		<cfset local.rootRateInterval = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@rateInterval)")>
		<cfset local.rootFreqID = xmlSearch(local.xmlSubscribeMember,"number(//set[@id='0']/subscription/@freqid)")>
		<cfset local.subTermDates = getSubTermDates(termDateRFID=local.termDateRFID, subTermFlag=local.subTermFlag)>
		<cfset local.subStartOverrideDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@startDateOverride)")>
		<cfset local.subEndOverrideDate = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@endDateOverride)")>
		
		<cfif (len(local.subStartOverrideDate) neq 0) OR (len(local.subEndOverrideDate) neq 0)>
			<cfset local.subPaymentDates = getSubPaymentDates(local.subStartOverrideDate, local.subEndOverrideDate, local.rootNumPayments, local.rootRateInterval)>
		<cfelseif structKeyExists(local,"subStartDate") and structKeyExists(local,"subEndDate") and isDate(local.subStartDate) and isDate(local.subEndDate)>
			<cfset local.subPaymentDates = getSubPaymentDates(local.subStartDate, local.subEndDate, local.rootNumPayments, local.rootRateInterval)>
		<cfelse>
			<cfset local.subPaymentDates = getSubPaymentDates(local.subTermDates.subTermStartDate, local.subTermDates.subTermEndDate, local.rootNumPayments, local.rootRateInterval)>
		</cfif>

		<cfset local.numPaymentsToUse = local.subPaymentDates.numPayments>
		<cfset local.subAmts = getSubscriptionAmounts(subXML=local.xmlSubscribeMember, numPaymentsToUse=local.numPaymentsToUse)>

		<cfset local.amountToCharge = local.subAmts.qryTotals.totalAmt>

		<cfif local.subAmts.qryUpFrontAmt.totalAmt gt 0>
			<cfset local.arrPaySchedule[1] = { date=now(), amount=numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__") }>
			<cfset local.firstPaymentMinimum = numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__")>
		</cfif>

		<cfset local.distribAmt = local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse>
		<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.subAmts.qryNonUpFrontAmt.totalAmt - (numberformat((local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

		<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
			<cfset local.loopAmt = 0>
			<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
			<cfset local.arrPaySchedule[local.loopCnt] = { date=now(), amount=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
		</cfloop>
		<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >
		
		<cfloop from="1" to="#ArrayLen(local.subPaymentDates.arrDates)#" index="local.thisP">
			<cfset local.arrPaySchedule[local.thisP].date = local.subPaymentDates.arrDates[local.thisP]>
		</cfloop>
		
		<cfset local.payOrderIndex = 1>

		<cfset local.strEventCollection.hInvoiceProfileIDs = valueList(local.subAmts.qryInvProfiles.invoiceProfileID)>
		
		<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopIndex">
			<cfset local.thisInvAmt = local.arrPaySchedule[local.loopIndex].amount>

			<cfset local.strEventCollection["ps_#local.loopIndex#_date"] = "#Month(local.arrPaySchedule[local.loopIndex].date)#/#Day(local.arrPaySchedule[local.loopIndex].date)#/#Year(local.arrPaySchedule[local.loopIndex].date)#">
			<cfset local.strEventCollection["ps_#local.loopIndex#_amt"] = local.thisInvAmt>

			<cfset local.strProfile = structNew()>
			
			<cfset local.invoiceAmtApplied = 0>
			<cfloop condition="(local.thisInvAmt-local.invoiceAmtApplied) gt 0.0 and local.payOrderIndex lte arrayLen(local.subAmts.payOrderArray)">

				<cfset local.currAmount = (local.subAmts.payOrderArray[local.payOrderIndex].total - local.subAmts.payOrderArray[local.payOrderIndex].totalapplied)>

				<cfif ((local.thisInvAmt-local.invoiceAmtApplied) gte local.currAmount)>
					<cfset local.invoiceAmtApplied = local.invoiceAmtApplied + local.currAmount>
					<cfset local.subAmts.payOrderArray[local.payOrderIndex].totalapplied = local.subAmts.payOrderArray[local.payOrderIndex].total>

					<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
						<cfset local.innerCurrAmount = (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied)>
						<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.total>
						<cfif not StructKeyExists(local.strProfile, "#local.thisProfileEntry.id#")>
							<cfset local.strProfile["#local.thisProfileEntry.id#"] = 0>
						</cfif>
						<cfset local.strProfile["#local.thisProfileEntry.id#"] = local.strProfile["#local.thisProfileEntry.id#"] + local.innerCurrAmount>
					</cfloop>
				<cfelseif ((local.thisInvAmt-local.invoiceAmtApplied) lt local.currAmount)>
					<cfset local.pctToUse = ((local.thisInvAmt-local.invoiceAmtApplied) / local.currAmount)>
					<cfset local.leftOver = numberformat((local.thisInvAmt-local.invoiceAmtApplied) - numberformat((((local.thisInvAmt-local.invoiceAmtApplied) / local.currAmount) * local.currAmount), "_.__"), "_.__")>
									
					<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
						<cfset local.innerCurrAmount = numberformat((local.thisProfileEntry.total - local.thisProfileEntry.totalapplied) * local.pctToUse, "_.__")>
						<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.totalapplied + local.innerCurrAmount>
							
						<cfif ((local.leftOver gt 0) AND (local.thisProfileEntry.totalapplied lt local.thisProfileEntry.total))>
							<cfif (local.leftOver gt (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
								<cfset local.innerCurrAmount = local.innerCurrAmount + (local.leftOver - (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
								<cfset local.leftOver = local.leftOver - ((local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
							<cfelse>
								<cfset local.innerCurrAmount += local.leftOver>
								<cfset local.leftOver = 0>
							</cfif>
							<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.totalapplied + local.innerCurrAmount>
						</cfif>
							
						<cfif not StructKeyExists(local.strProfile, "#local.thisProfileEntry.id#")>
							<cfset local.strProfile["#local.thisProfileEntry.id#"] = 0>
						</cfif>
						<cfset local.strProfile["#local.thisProfileEntry.id#"] = local.strProfile["#local.thisProfileEntry.id#"] + local.innerCurrAmount>
					</cfloop>
					
					<cfset local.subAmts.payOrderArray[local.payOrderIndex].totalapplied = numberformat(local.subAmts.payOrderArray[local.payOrderIndex].totalapplied + local.thisInvAmt-local.invoiceAmtApplied, "_.__")>
					<cfset local.invoiceAmtApplied = local.thisInvAmt>
				</cfif>

				<cfif ((local.subAmts.payOrderArray[local.payOrderIndex].total-local.subAmts.payOrderArray[local.payOrderIndex].totalapplied) lte 0.0)>
					<cfset local.payOrderIndex = local.payOrderIndex + 1>
				</cfif>
			
			</cfloop>

			<cfloop array="#StructKeyArray(local.strProfile)#" index="local.thisProfileKey">
				<cfset local.loopCurrAmt = local.strProfile["#local.thisProfileKey#"]>
				<cfset local.strEventCollection["hps_#local.loopIndex#_#local.thisProfileKey#_amt"] = numberformat(local.loopCurrAmt, "_.__")>
				<cfset local.strEventCollection["hps_#local.loopIndex#_#local.thisProfileKey#_appearOnThisInvoice"] = true>
			</cfloop>
		</cfloop>

		<cfset local.confirmRetStruct = doConfirm(subXML=local.xmlSubscribeMember, strEventCollection=local.strEventCollection, bypassQueue=arguments.bypassQueue)>
		<cfif local.confirmRetStruct.success eq 0>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errcode = local.confirmRetStruct.keyExists("errcode") ? local.confirmRetStruct.errcode : "DOCONFIRMFAIL">
			<cfset local.returnStruct.errReason = local.confirmRetStruct.keyExists("errMessage") ? local.confirmRetStruct.errMessage : "Unable to create invoices">
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getRates" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="isRenewal" type="boolean" required="true">
		<cfargument name="rateUID" type="string" required="false" default="">
		<cfargument name="overridePerms" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery name="local.qryRates" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			
			declare @FID int, @memberid int, @siteID int;
			set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			set @siteid = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;
			set @memberid = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;
			
			select rfid, rateAmt, numInstallments, allowFrontEnd, frequencyName, frequencyShortName, frequency, frequencyID, rateName, rateUID, frequencyUID
			from (
				select rf.rfid, rf.rateAmt, rf.numInstallments, rf.allowFrontEnd, f.frequencyName, f.frequencyShortName, f.frequency, f.frequencyID, r.rateName, r.uid as rateUID, UPPER(convert(varchar(100), f.uid)) as frequencyUID, count(rfmp.rfmpid) as rfmpidCount
				from dbo.sub_subscriptions as s
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
				inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and <cfif arguments.isRenewal>r.isRenewalRate = 1<cfelse>r.isRenewalRate = 0</cfif>
					and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
					<cfif len(arguments.rateUID) gt 0>and r.uid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateUID#"></cfif>
				<cfif not arguments.overridePerms>
					INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteID
						AND srfrp.siteResourceID = r.siteResourceID
						AND srfrp.functionID = @FID
					INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
						AND srfrp.rightPrintID = gprp.rightPrintID
					inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
					    and m.memberID = @memberID		
				</cfif>					
				inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.rateAmt >= 0 and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.siteID = @siteID
					and f.status = 'A'
				left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid and rfmp.status = 'A' 
				where s.subscriptionID = <cfqueryparam value="#arguments.subscriptionID#" cfsqltype="CF_SQL_INTEGER">
				group by rf.rfid, rf.rateAmt, rf.numInstallments, rf.allowFrontEnd, f.frequencyName, f.frequencyShortName, f.frequency, f.frequencyID, r.rateName, r.uid, f.uid
			) x 
			<cfif not arguments.overridePerms>
				where x.rfmpidCount > 0
			</cfif>;			
		</cfquery>
		
		<cfreturn local.qryRates>	
	</cffunction>

	<cffunction name="getSubTermDates" access="public" output="false" returntype="struct">
		<cfargument name="termDateRFID" type="numeric" required="true">
		<cfargument name="subTermFlag" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = false>
		<cfset local.returnStruct.subTermStartDate = ''>
		<cfset local.returnStruct.subTermEndDate = ''>
		<cfset local.returnStruct.subTermGraceEndDate = ''>

		<cfstoredproc procedure="sub_getTermDates" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.termDateRFID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_CHAR" value="#arguments.subTermFlag#">
			<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
			<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" null="true">
			<cfprocresult name="local.qryGetTermDates">
		</cfstoredproc>

		<cfif len(local.qryGetTermDates.subTermStartDate)>
			<cfset local.returnStruct.subTermStartDate = DateFormat(local.qryGetTermDates.subTermStartDate, "mm/dd/yyyy")>
			<cfset local.returnStruct.subTermEndDate = DateFormat(local.qryGetTermDates.subTermEndDate, "mm/dd/yyyy")>
			<cfset local.returnStruct.subTermGraceEndDate = DateFormat(local.qryGetTermDates.subTermGraceEndDate, "mm/dd/yyyy")>
			<cfset local.returnStruct.success = true>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>	
	
	<cffunction name="getPrevSubTermDates" access="public" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriberID" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.subTermStartDate = ''>
		<cfset local.returnStruct.subTermEndDate = ''>
		<cfset local.returnStruct.subTermGraceEndDate = ''>
		<cfset local.returnStruct.subTermStatusCode = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPrevDates">
			select top 1 s.subscriberID, s.subStartDate, s.subEndDate, s.graceEndDate, st.statusCode
			from dbo.sub_subscribers s
			inner join dbo.sub_statuses st on st.statusID = s.statusID
				and st.statusCode not in ('D','X')
			inner join dbo.sub_subscribers sPrev on sPrev.subscriptionID = s.subscriptionID
				and sPrev.subscriberID <> s.subscriberID
				and sPrev.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriberID#">
			where s.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			order by s.subEndDate DESC
		</cfquery>

		<cfset local.returnStruct.subTermStatusCode = local.qryPrevDates.statusCode>

		<cfset local.subTermStartDate = local.qryPrevDates.subStartDate>
		<cfif len(local.subTermStartDate) gt 0>
			<cfset local.returnStruct.subTermStartDate = DateFormat(local.subTermStartDate, "m/d/yyyy")>
		</cfif>

		<cfset local.subTermEndDate = local.qryPrevDates.subEndDate>
		<cfif len(local.subTermEndDate) gt 0>
			<cfset local.returnStruct.subTermEndDate = DateFormat(local.subTermEndDate, "m/d/yyyy")>
		</cfif>

		<cfset local.subTermGraceEndDate = local.qryPrevDates.graceEndDate>
		<cfif len(local.subTermGraceEndDate) gt 0>
			<cfset local.returnStruct.subTermGraceEndDate = DateFormat(local.subTermGraceEndDate, "m/d/yyyy")>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>	
	
	<cffunction name="getSubPaymentDates" access="public" output="false" returntype="struct">
		<cfargument name="subStartDate" type="date" required="true">
		<cfargument name="subEndDate" type="date" required="true">
		<cfargument name="numPayments" type="numeric" required="true">
		<cfargument name="monthlyInterval" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qrySubPayments">
			set nocount on;

			declare @startdate datetime, @enddate datetime, @today datetime, @interval int, @numpayment int, 
				@secondPaymentDate datetime, @lastPaymentMonthsToAdd int, @intervalsThatFit int;
			
			select @startdate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.subStartDate#">,
				@enddate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.subEndDate#">,
				@today = DateAdd(Day, DateDiff(Day, 0, GetDate()), 0),
				@numpayment = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.numPayments#">,
				@interval = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.monthlyInterval#">;
			
			IF DateDiff(dd, @startdate, @today) > 0
				select @startdate = @today;
			
			select @secondPaymentDate = dateadd(mm,@interval,@startdate),
				@lastPaymentMonthsToAdd = (@numpayment-1) * @interval,
				@intervalsThatFit = datediff(mm,@startdate,@enddate) / @interval;
			
			with dateCTE as (
				select 1 as paymentNumber, @startdate as paymentDate
			
				union all
				
				select dateCTE.paymentNumber + 1 as paymentNumber, dateadd(mm,@interval,dateCTE.paymentDate) as paymentDate
				from dateCTE
				where dateadd(mm,@interval,dateCTE.paymentDate) < @enddate
				and dateCTE.paymentNumber < @numpayment
			)
			select paymentNumber, convert(varchar, paymentDate, 101) as paymentDate from dateCTE;
		</cfquery>

		<cfset local.returnStruct.arrDates = ArrayNew(1)>
		<cfloop query="local.returnStruct.qrySubPayments">
			<cfset ArrayAppend(local.returnStruct.arrDates, local.returnStruct.qrySubPayments.paymentDate)>
		</cfloop>

		<cfset local.returnStruct.numPayments = local.returnStruct.qrySubPayments.recordCount>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getDatesToUseForExistingSubs" access="private" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="subStartDate" type="string" required="true">
		<cfargument name="subEndDate" type="string" required="true">
		<cfargument name="subGraceEndDate" type="string" required="true">
		<cfargument name="updActiveDate" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.subStartDate = "">
		<cfset local.returnStruct.subEndDate = "">
		<cfset local.returnStruct.subGraceEndDate = "">
		<cfset local.returnStruct.dateOverride = false>
		<cfset local.returnStruct.success = true>

		<cfif XMLSearch(arguments.subXML,"count(//set[@id='0']/subscription)") gt 0>
		
			<!--- check for overrides --->
			<cfset local.subExistTermStartDate = xmlSearch(arguments.subXML,"string(//set[@id='0']/subscription/@startDateOverride)")>
			<cfset local.subExistTermEndDate = xmlSearch(arguments.subXML,"string(//set[@id='0']/subscription/@endDateOverride)")>
			<cfset local.subExistTermGraceEndDate = xmlSearch(arguments.subXML,"string(//set[@id='0']/subscription/@graceEndDateOverride)")>
		
			<cfif (len(local.subExistTermStartDate) eq 0) OR (len(local.subExistTermEndDate) eq 0)>
				<cfset local.returnStruct.subStartDate = arguments.subStartDate>
				<!--- preserves the hour, thus the TZ --->
				<cfset local.daysDiff = DateDiff("d", local.returnStruct.subStartDate, Now())>
				<cfif local.daysDiff gt 0>
					<cfif len(arguments.updActiveDate)>
						<cfset local.returnStruct.subStartDate = arguments.updActiveDate>
					<cfelse>
						<cfset local.subCurrDate = DateAdd("d", local.daysDiff, local.returnStruct.subStartDate)>
						<cfset local.subCurrDate = DateFormat(local.subCurrDate, "yyyy-mm-dd") & " " & TimeFormat(local.subCurrDate, "HH:mm:ss.l")>
						<cfset local.returnStruct.subStartDate = local.subCurrDate>
					</cfif>
				</cfif>				
				
				<cfset local.returnStruct.subEndDate = arguments.subEndDate>
				<cfset local.returnStruct.subGraceEndDate = arguments.subGraceEndDate>
			<cfelse>
				<cfset local.returnStruct.dateOverride = true>
				<cfset local.returnStruct.subStartDate = DateFormat(local.subExistTermStartDate, "mm/dd/yyyy")>
				<cfset local.returnStruct.subEndDate = DateFormat(local.subExistTermEndDate, "mm/dd/yyyy")>
				<cfif len(local.subExistTermGraceEndDate) gt 0>
					<cfset local.returnStruct.subGraceEndDate = DateFormat(local.subExistTermGraceEndDate, "mm/dd/yyyy")>
				</cfif>
	
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="getDateOverrideCode" access="public" output="false" returntype="string">
		<cfargument name="dateOverride" type="boolean" required="true">
		<cfargument name="subStartDate" type="string" required="true">
		<cfargument name="updActiveDate" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retCode = ''>

		<cfif len(arguments.updActiveDate) gt 0>
			<cfset local.activeDateCompare = (DateCompare(DateFormat(arguments.updActiveDate, "mm/dd/yyyy"), DateFormat(arguments.subStartDate, "mm/dd/yyyy")))>
			<cfset local.activeDateFormat = DateFormat(arguments.updActiveDate, "mm/dd/yy")>
		<cfelse>
			<cfset local.activeDateCompare = 0>
			<cfset local.activeDateFormat = "">
		</cfif>

		<cfsavecontent variable="local.retCode">
			<cfoutput>
			
			<cfif (arguments.dateOverride) OR (local.activeDateCompare eq -1)>
				<cfif (local.activeDateCompare eq -1)>
					<cfset local.subTestStartDate = local.activeDateFormat>
				<cfelse>
					<cfset local.subTestStartDate = DateFormat(arguments.subStartDate, "mm/dd/yyyy")>
				</cfif>
				update sh
				set sh.updateDate = case 
									when DATEDIFF(day, sh.updateDate, '#local.subTestStartDate#') < 0 then DateAdd(dd, DATEDIFF(day, sh.updateDate, '#local.subTestStartDate#'), sh.updateDate) 
									else sh.updateDate 
									end
				from dbo.sub_statusHistory sh
				inner join sub_statuses st on st.statusID = sh.statusID
					and st.statusCode = 'R' or st.statusCode = 'O'
				where sh.orgID = @orgID and sh.subscriberID = @subscriberID
			</cfif>

			<cfif len(arguments.updActiveDate) gt 0>
				select @offerDate = NULL
				select @offerDate = sh.updateDate
					from dbo.sub_statusHistory sh
					inner join sub_statuses st on st.statusID = sh.statusID
						and st.statusCode = 'O'
					where sh.orgID = @orgID and sh.subscriberID = @subscriberID
				
				IF @offerDate IS NOT NULL
				BEGIN
					update sh
					set sh.updateDate = case 
										when DATEDIFF(ss, '#local.activeDateFormat#', @offerDate) > 0 then DateAdd(ss, DATEDIFF(second, '#local.activeDateFormat#', @offerDate)+1, '#local.activeDateFormat#')
										else '#local.activeDateFormat# 00:00:00.0' 
										end
					from dbo.sub_statusHistory sh
					inner join sub_statuses st on st.statusID = sh.statusID
						and st.statusCode = 'P'
					where sh.orgID = @orgID and sh.subscriberID = @subscriberID
					
					update sh
					set sh.updateDate = case 
										when DATEDIFF(ss, '#local.activeDateFormat#', @offerDate) > 0 then DateAdd(ss, DATEDIFF(second, '#local.activeDateFormat#', @offerDate)+2, '#local.activeDateFormat#')
										else '#local.activeDateFormat# 00:00:01.0' 
										end
					from dbo.sub_statusHistory sh
					inner join sub_statuses st on st.statusID = sh.statusID
						and st.statusCode = 'A'
					where sh.orgID = @orgID and sh.subscriberID = @subscriberID
				END								
			</cfif>			
			
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.retCode>
	</cffunction>
	
	<cffunction name="getRecognitionDates" access="package" output="false" returntype="struct">
		<cfargument name="useRFID" type="numeric" required="true">
		<cfargument name="saleTransactionDate" type="date" required="true">
		<cfargument name="subStartDate" type="date" required="true">
		<cfargument name="subEndDate" type="date" required="true">
		<cfargument name="rootsubTermFlag" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfquery name="local.qryGetRecogDate" datasource="#application.dsn.membercentral.dsn#">
			select top 1 r.recogAFStartDate, r.recogAFEndDate
			from dbo.sub_rates as r
			inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
			where rf.rfid = <cfqueryparam value="#arguments.useRFID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.retStruct.recogStartDate = DateFormat(local.qryGetRecogDate.recogAFStartDate,'m/d/yyyy')>
		<cfset local.retStruct.recogEndDate = "#dateformat(local.qryGetRecogDate.recogAFEndDate,'m/d/yyyy')# 23:59:59.997">
		
		<!--- if blank, default to sub dates --->
		<cfif NOT len(local.retStruct.recogStartDate)>
			<cfset local.retStruct.recogStartDate = dateformat(arguments.subStartDate,'m/d/yyyy')>
		</cfif>
		<cfif NOT len(local.retStruct.recogEndDate)>
			<cfset local.retStruct.recogEndDate = "#dateformat(arguments.subEndDate,'m/d/yyyy')# 23:59:59.997">
		</cfif>

		<!--- recognition start date cannot be before sale transaction date --->
		<cfif dateCompare(arguments.saleTransactionDate,local.retStruct.recogStartDate) is 1>
			<cfset local.retStruct.recogStartDate = dateformat(arguments.saleTransactionDate,'m/d/yyyy')>
		</cfif>

		<!--- recognition start date cannot be before subscription start date --->
		<cfif dateCompare(arguments.subStartDate,local.retStruct.recogStartDate) is 1>
			<cfset local.retStruct.recogStartDate = dateformat(arguments.subStartDate,'m/d/yyyy')>
		</cfif>

		<!--- If true anniversary mode --->
		<cfif arguments.rootsubTermFlag eq "C">
			<!--- Attempt to spread recognition over original number of days as configured --->
			<cfset local.recognitionDays = DateDiff("d", local.qryGetRecogDate.recogAFStartDate, local.qryGetRecogDate.recogAFEndDate)>
			<cfset local.retStruct.recogEndDate = DateAdd("d", local.recognitionDays, local.retStruct.recogStartDate)>
		</cfif>

		<!--- recognition end date cannot be after sub end date --->
		<cfif dateCompare(arguments.subEndDate,local.retStruct.recogEndDate) is -1>
			<cfset local.retStruct.recogEndDate = "#dateformat(arguments.subEndDate,'m/d/yyyy')# 23:59:59.997">
		</cfif>

		<!--- recognition start date must be before recognition end date --->
		<cfif DateCompare(local.retStruct.recogStartDate,local.retStruct.recogEndDate) is 1>
			<cfset local.retStruct.recogEndDate = "#dateformat(local.retStruct.recogStartDate,'m/d/yyyy')# 23:59:59.997">
		</cfif>

		<cfset local.retStruct.recogStartDate = dateformat(local.retStruct.recogStartDate,'m/d/yyyy')>
		<cfset local.retStruct.recogEndDate = "#dateformat(local.retStruct.recogEndDate,'m/d/yyyy')# 23:59:59.997">

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSubscriptionDates" access="public" output="false" returntype="struct">
		<cfargument name="useRFID" type="numeric" required="true">
		<cfargument name="subTermFlag" type="string" required="true">
		<cfargument name="startDate" type="string" required="true">
		<cfargument name="overrideCalc" type="boolean" required="false" default="false">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.subStartDate = "">
		<cfset local.returnStruct.subEndDate = "">
		<cfset local.returnStruct.graceEndDate = "">

		<cfif arguments.overrideCalc>
			<cfset local.subTermFlag = "C">
		<cfelse>
			<cfset local.subTermFlag = arguments.subTermFlag>
		</cfif>

		<cfstoredproc procedure="sub_getTermDates" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.useRFID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_CHAR" value="#local.subTermFlag#">
			<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.startDate#">
			<cfprocparam type="in" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" null="true">
			<cfprocresult name="local.qryGetTermDates">
		</cfstoredproc>

		<cfif len(local.qryGetTermDates.subTermStartDate)>
			<cfset local.returnStruct.subStartDate = DateFormat(local.qryGetTermDates.subTermStartDate, "mm/dd/yyyy")>
			<cfset local.returnStruct.subEndDate = DateFormat(local.qryGetTermDates.subTermEndDate, "mm/dd/yyyy")>
			<cfset local.returnStruct.graceEndDate = DateFormat(local.qryGetTermDates.subTermGraceEndDate, "mm/dd/yyyy")>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubscriptionAmounts" access="public" output="false" returntype="struct">
		<cfargument name="subXML" type="xml" required="true">
		<cfargument name="numPaymentsToUse" type="numeric" required="true">
		<cfargument name="invoiceMonths" type="numeric" required="false" default="0">
		<cfargument name="includeExtraSubInfo" type="boolean" required="false" default="false">
		
		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>
		
		<cfset local.topSetUID = XMLSearch(arguments.subXML,"string(//set[@id='0']/@uid)")> 
		<cfset local.rootFreqID = xmlSearch(arguments.subXML,"number(//set[@id='0']/subscription/@freqid)")>
		<cfset local.payOrderList = ''>

		<cfsavecontent variable="local.sqlBuilding">
			<cfloop array="#XMLSearch(arguments.subXML,'//subscription')#" index="local.thisSub">
				<cfif (arguments.invoiceMonths eq 0) AND NOT (StructKeyExists(local.thisSub.xmlAttributes, "deleteme") AND (local.thisSub.xmlAttributes.deleteme eq 1))>
					<cfif local.thisSub.xmlAttributes.pcisfree>
						<cfset local.rateAmtToUse = 0.00>
						<cfset local.rateInstallmentsToUse = 1>
					<cfelseif (local.thisSub.xmlAttributes.freqID neq local.rootFreqID)>
						<cfset local.rateAmtToUse = local.thisSub.xmlAttributes.fullrateAmt>
						<cfset local.rateInstallmentsToUse = 1>
					<cfelse>
						<cfset local.rateAmtToUse = local.thisSub.xmlAttributes.rateAmt>
						<cfset local.rateInstallmentsToUse = local.thisSub.xmlAttributes.rateInstallments>
					</cfif>
					<cfset local.loopUseRate = 0.00>
					
					<cfif local.thisSub.xmlAttributes.alreadysub neq true 
						OR local.thisSub.xmlAttributes.currstatus eq 'R' 
						OR local.thisSub.xmlAttributes.currstatus eq 'O'
						OR (
							(local.thisSub.xmlAttributes.alreadysub eq true AND local.thisSub.xmlAttributes.hasinvoiceid neq 'true')
							AND
							StructKeyExists(local.thisSub.xmlAttributes, "readdme") 
							AND 
							local.thisSub.xmlAttributes.readdme eq 1
						)>
						<cfset local.currRateToUse = getRateToUse(useRates=local.thisSub.xmlAttributes.userates, rateAmt=local.rateAmtToUse, 
							rateInstallments=local.rateInstallmentsToUse, numPaymentsToUse=arguments.numPaymentsToUse, 
							pcPctOff=local.thisSub.xmlAttributes.pcpctoff, pcRateAmt=local.thisSub.xmlAttributes.pcrateamt)>
						<cfset local.loopUseRate = local.currRateToUse.rateTotal>

					<cfelseif local.thisSub.xmlAttributes.alreadysub eq true AND local.thisSub.xmlAttributes.pricechanged eq 1>
						<cfset local.loopUseRate = val(local.thisSub.xmlAttributes.pcrateamt)>
					
					<cfelse>					
						<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryExistingInvoiceTotal">
							SET NOCOUNT ON;

							declare @orgID int;

							IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
								DROP TABLE ##mcSubscribersForAcct;
							IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
								DROP TABLE ##mcSubscriberTransactions;

							CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
							CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
								invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
								amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
								assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

							INSERT INTO ##mcSubscribersForAcct (subscriberID)
							VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#int(val(local.thisSub.xmlAttributes.sid))#">);

							select top 1 @orgID = s.orgID
							from dbo.sub_subscribers as s
							inner join ##mcSubscribersForAcct as tmp on tmp.subscriberID = s.subscriberID;

							EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

							select s.subscriberID, s.subscriptionID, sum(st.amount) as sumTotal
							from dbo.sub_subscribers s
							inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
							inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
							where s.orgID = @orgID
							and ins.status = 'Pending'
							group by s.subscriberID, s.subscriptionID;

							IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
								DROP TABLE ##mcSubscribersForAcct;
							IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
								DROP TABLE ##mcSubscriberTransactions;
						</cfquery>

						<cfset local.loopUseRate = val(local.qryExistingInvoiceTotal.sumTotal)>
					</cfif>
			
					<cfif val(local.loopUseRate) eq 0>
						<cfset local.loopForceUp = 0>
						<cfset local.loopPayOrder = "0000.">
					<cfelseif local.thisSub.xmlAttributes.forceUpfront eq 1>
						<cfset local.loopForceUp = 1>
						<cfset local.loopPayOrder = "0000.">
					<cfelse>
						<cfset local.loopForceUp = 0>
						<cfset local.loopPayOrder = "#numberformat(local.thisSub.xmlAttributes.payOrder, "0000.")#">
						<cfset local.loopParentSetUID = XMLSearch(arguments.subXML,"string(//subscription[@uid='#local.thisSub.xmlAttributes.uid#']/../@uid)")>	
	
						<cfloop condition="local.loopParentSetUID neq local.topSetUID">	
							<!--- we removed the addon paymentorder column, but it looks like it is still needed here, so hardcode to 1. --->
							<cfset local.loopPayOrder = "#numberformat(1, "0000.")#" & local.loopPayOrder>
							<cfset local.loopParentSubPayOrder = XMLSearch(arguments.subXML,"string(//set[@uid='#local.loopParentSetUID#']/../@payOrder)")>	
							<cfset local.loopPayOrder = "#numberformat(local.loopParentSubPayOrder, "0000.")#" & local.loopPayOrder>
							<cfset local.loopParentSetUID = XMLSearch(arguments.subXML,"string(//set[@uid='#local.loopParentSetUID#']/../../@uid)")>
						</cfloop>
						
						<cfif Len(local.loopPayOrder) gt 5>
							<!--- chop off the top sub, it's an anomaly--->
							<cfset local.loopPayOrder = Right(local.loopPayOrder, (Len(local.loopPayOrder)-5))>
							<cfset local.loopPayOrder = Left(local.loopPayOrder, 5) & Replace(Right(local.loopPayOrder, (Len(local.loopPayOrder)-5)), "0001.", "", "all")>
						</cfif>
					</cfif>

					<cfif ListContains(local.payOrderList, local.loopPayOrder) eq 0>
						<cfset local.payOrderList = ListAppend(local.payOrderList, local.loopPayOrder)>
					</cfif>
					
					<cfset local.loopSubAmt = val(local.loopUseRate)>
					<cfif local.thisSub.xmlAttributes.allowRateGLOverride AND local.thisSub.xmlAttributes.rateGLAccountID neq 0>
						<cfset local.loopInvProfileID = local.thisSub.xmlAttributes.rateInvoiceProfileID>
					<cfelse>
						<cfset local.loopInvProfileID = local.thisSub.xmlAttributes.subInvoiceProfileID>
					</cfif>
					
					<cfoutput>
					insert into @tblAmts (amount, payOrder, forceUpFront, subscriptionID, invoiceProfileID, rateid)
					values (#local.loopSubAmt#, '#local.loopPayOrder#', #local.loopForceUp#, #local.thisSub.xmlAttributes.id#, #local.loopInvProfileID#, 
						<cfif local.thisSub.xmlAttributes.useRates is 1>#local.thisSub.xmlAttributes.rateID#<cfelse>null</cfif>
					);
					</cfoutput>
				</cfif>
			</cfloop>
		</cfsavecontent>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubAmts">
			SET NOCOUNT ON;

			declare @rateNameOverride varchar(15) = 'Rate Overridden';
			declare @tblAmts TABLE (rowID int identity(1,1), amount decimal(18,2), payOrder varchar(100), forceUpFront bit,
				subscriptionID int, subscriptionName varchar(300), invoiceProfileID int, invoiceProfileName varchar(50), 
				rateID int, rateName varchar(200));

			#preserveSingleQuotes(trim(local.sqlBuilding))#

			update tbl
			set tbl.subscriptionName = s.subscriptionName
			from @tblAmts tbl
			inner join dbo.sub_subscriptions s on s.subscriptionID = tbl.subscriptionID;

			update tbl
			set tbl.rateName = r.rateName
			from @tblAmts tbl
			inner join dbo.sub_rates r on r.rateID = tbl.rateID;

			update @tblAmts
			set rateName = @rateNameOverride
			where rateID is null;

			update tbl
			set tbl.invoiceProfileName = ip.profileName
			from @tblAmts tbl
			inner join dbo.tr_invoiceProfiles ip on ip.profileID = tbl.invoiceProfileID;

			select rowID, amount, payOrder, forceUpFront, subscriptionID, subscriptionName, invoiceProfileID, 
				invoiceProfileName, rateID, rateName
			from @tblAmts;
		</cfquery>

		<cfset local.payOrderList = ListSort(local.payOrderList, "Text")>

		<cfquery dbtype="query" name="local.qryInvProfileAmts">
			select sum(amount) as totalAmt, payOrder, forceUpFront, invoiceProfileID, invoiceProfileName
			from [local].qrySubAmts
			group by payOrder, forceUpFront, invoiceProfileID, invoiceProfileName
			order by payOrder, forceUpFront DESC, invoiceProfileID, invoiceProfileName
		</cfquery>

		<!--- Array for JS --->
		<cfset local.payOrderArray = ArrayNew(1)>
		<cfloop list="#local.payOrderList#" index="local.thisPayOrderPath">
			<cfset local.payOrderStructEntry = StructNew()>
			<cfset local.payOrderStructEntry.payOrder = "#local.thisPayOrderPath#">
			<cfset local.payOrderStructEntry.total = 0.00>
			<cfset local.payOrderStructEntry.rate = 0.00 + 0.00>
			<cfset local.payOrderStructEntry.diff = 0.00 + 0.00>
			<cfset local.payOrderStructEntry.totalApplied = 0.00 + 0.00>
			<cfset local.payOrderStructEntry.profiles = ArrayNew(1)>

			<cfquery dbtype="query" name="local.qryProfileAmt">
				select sum(totalAmt) as totalAmt, invoiceProfileName, invoiceProfileID
				from [local].qryInvProfileAmts
				where payOrder = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisPayOrderPath#">
				group by invoiceProfileName, invoiceProfileID
			</cfquery>
			<cfloop query="local.qryProfileAmt">
				<cfset local.profileEntry = StructNew()>
				<cfset local.profileEntry.name = local.qryProfileAmt.invoiceProfileName>
				<cfset local.profileEntry.id = local.qryProfileAmt.invoiceProfileID>
				<cfset local.profileEntry.total = local.qryProfileAmt.totalAmt>
				<cfset local.profileEntry.totalApplied = 0.00 + 0.00>
				<cfset local.profileEntry.currAmount = 0.00 + 0.00>

				<cfset local.profileEntry.subs = ArrayNew(1)>
				
				<cfquery dbtype="query" name="local.qryInvProfileSubs">
					select amount, subscriptionName + ' - ' + rateName as subscriptionName, subscriptionID
					from [local].qrySubAmts
					where invoiceProfileID = #local.qryProfileAmt.invoiceProfileID#
					and payOrder = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisPayOrderPath#">
					order by rowID
				</cfquery>
				<cfloop query="local.qryInvProfileSubs">
					<cfset local.subEntry = StructNew()>
					<cfset local.subEntry.name = replaceNoCase(local.qryInvProfileSubs.subscriptionName,' - Rate Overridden','','ALL')>
					<cfset local.subEntry.id = local.qryInvProfileSubs.subscriptionID>
					<cfset local.subEntry.total = local.qryInvProfileSubs.amount>
					<cfset local.subEntry.totalApplied = 0.00 + 0.00>
					<cfif arguments.includeExtraSubInfo>
						<cfset local.subInfoArr = XMLSearch(arguments.subXML,"//subscription[@id='#local.qryInvProfileSubs.subscriptionID#']")>
						<cfset local.subInfo = local.subInfoArr[1]>
						<cfset local.subEntry.uid = local.subInfo.xmlAttributes.uid>
						
						<cfif int(val(local.subInfo.xmlAttributes.sid)) eq 0 AND 
									(local.subInfo.xmlAttributes.allowRateGLOverride eq 1) AND (local.subInfo.xmlAttributes.rateGLAccountID neq 0)>
							<cfset local.subEntry.GLAccountID = int(val(local.subInfo.xmlAttributes.rateGLAccountID))>
							<cfset local.subEntry.invProfileID = local.thisSub.xmlAttributes.rateInvoiceProfileID>
						<cfelse>
							<cfset local.subEntry.GLAccountID = int(val(local.subInfo.xmlAttributes.glaccountidtouse))>
							<cfset local.subEntry.invProfileID = local.thisSub.xmlAttributes.subInvoiceProfileID>
						</cfif>
						
						<cfset local.subEntry.subscriptionID = int(val(local.subInfo.xmlAttributes.id))>
						<cfset local.subEntry.subscriberID = int(val(local.subInfo.xmlAttributes.sid))>
						<cfset local.subEntry.subHasInvoiceID = local.subInfo.xmlAttributes.hasinvoiceid>
						<cfset local.subEntry.useRate = 0.00>						
						<cfset local.subEntry.rateApplied = 0.00>						
						<cfset local.subStatus = local.subInfo.xmlAttributes.currStatus>
						<cfif Len(local.subStatus) eq 0>
							<cfset local.subStatus = 'A'>
						</cfif>
						<cfset local.subEntry.subStatus = local.subStatus>
						<cfif StructKeyExists(local.subInfo.xmlAttributes,"readdme")>
							<cfset local.subEntry.reAddMe = local.subInfo.xmlAttributes.readdme>
						<cfelse>
							<cfset local.subEntry.reAddMe = 0>
						</cfif>
						<cfset local.subEntry.isSinglePayment = true>
					</cfif>

					<cfset ArrayAppend(local.profileEntry.subs, local.subEntry)>
				</cfloop>
				
				<cfset local.payOrderStructEntry.total = local.payOrderStructEntry.total + local.qryProfileAmt.totalAmt>
				<cfset ArrayAppend(local.payOrderStructEntry.profiles, local.profileEntry)>
			</cfloop>

			<cfset ArrayAppend(local.payOrderArray, local.payOrderStructEntry)>
		</cfloop>

		<cfquery dbtype="query" name="local.qryTotals">
			select sum(totalAmt) as totalAmt
			from [local].qryInvProfileAmts
		</cfquery>
		<cfset local.amountToCharge = local.qryTotals.totalAmt>

		<cfquery dbtype="query" name="local.qryUpFrontAmt">
			select sum(totalAmt) as totalAmt
			from [local].qryInvProfileAmts
			where forceUpFront = 1
		</cfquery>

		<cfquery dbtype="query" name="local.qryNonUpFrontAmt">
			select sum(totalAmt) as totalAmt
			from [local].qryInvProfileAmts
			where forceUpFront = 0
		</cfquery>
		
		<cfif not local.qryNonUpFrontAmt.recordcount>
			<cfset local.qryNonUpFrontAmt = QueryNew("totalAmt", "Double")>
			<cfset QueryAddRow(local.qryNonUpFrontAmt, 1)>
			<cfset QuerySetCell(local.qryNonUpFrontAmt, "totalAmt", "0", 1)>
		</cfif>
		
		<cfquery dbtype="query" name="local.qryInvProfiles">
			select distinct invoiceProfileID, invoiceProfileName
			from [local].qryInvProfileAmts
			order by invoiceProfileName
		</cfquery>

		<cfset local.retStruct.qrySubAmts = local.qrySubAmts>
		<cfset local.retStruct.payOrderList = local.payOrderList>
		<cfset local.retStruct.payOrderArray = local.payOrderArray>
		<cfset local.retStruct.qryTotals = local.qryTotals>
		<cfset local.retStruct.qryUpFrontAmt = local.qryUpFrontAmt>
		<cfset local.retStruct.qryNonUpFrontAmt = local.qryNonUpFrontAmt>
		<cfset local.retStruct.qryInvProfiles = local.qryInvProfiles>

		<cfreturn local.retStruct>
	</cffunction>	

	<cffunction name="getQualifiedRateCount" access="package" output="false" returntype="numeric">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="useRenewalRates" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		
		<cfquery name="local.rateCount" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @memberID int, @subscriptionID int, @functionID int,  @currentDate datetime, @useRenewalRates bit, @siteID int;
			set @currentDate = getdate();
			set @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			set @subscriptionID = <cfqueryparam value="#arguments.subscriptionID#" cfsqltype="CF_SQL_INTEGER">;
			set @memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">;
			set @useRenewalRates=<cfqueryparam value="#arguments.useRenewalRates#" cfsqltype="CF_SQL_BIT">;

			SELECT @siteID = t.siteID
			FROM dbo.sub_subscriptions as s
			INNER JOIN dbo.sub_types as t on t.typeID = s.typeID
			WHERE s.subscriptionID = @subscriptionID;

			select count(*) as qualifiedRates
			from dbo.sub_subscriptions subs
			inner join dbo.sub_rates r on r.scheduleID = subs.scheduleID
				and r.isRenewalRate = @useRenewalRates
				and @currentDate between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
			inner join dbo.cms_siteResources sr on sr.siteID = @siteID
				and sr.siteResourceID = r.siteResourceID
				and sr.siteResourceStatusID = 1
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
				and srfrp.siteResourceID = sr.siteResourceID
				and srfrp.functionID = @functionID 
			inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
				and gprp.rightPrintID = srfrp.rightPrintID
			inner join dbo.ams_members m on m.memberID = @memberID
				and m.groupPrintID = gprp.groupPrintID
			where subs.subscriptionID = @subscriptionID;
		</cfquery>

		<cfreturn val(local.rateCount.qualifiedRates)>
	</cffunction>
	
	<cffunction name="getStateZipForTax" access="private" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">

		<cfset var qryStateZipDetails = "">

		<cfquery name="qryStateZipDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">,
				@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">

			SELECT isnull(ma.stateID,0) as stateIDForTax, isnull(ma.postalCode,'') as zipForTax
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activememberID
			LEFT OUTER JOIN dbo.ams_memberAddresses as ma 
				INNER JOIN dbo.ams_memberAddressTags as matag on matag.orgID = @orgID and matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
				INNER JOIN dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
				ON ma.orgID = @orgID and ma.memberid = m2.memberID
			WHERE m.orgID = @orgID
			AND m.memberID = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStateZipDetails>
	</cffunction>

	<cffunction name="getMaxFrequencyInstallments" access="public" output="yes" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryMaxInstallments" datasource="#application.dsn.membercentral.dsn#" cachedwithin="#createTimeSpan(0,0,1,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT max(frequency) as maxInstallments
			FROM dbo.sub_frequencies
			WHERE siteID =  <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn max(12,val(local.qryMaxInstallments.maxInstallments))>
	</cffunction>

	<cffunction name="loadSubXML" access="private" output="false" returntype="xml">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryLookupXML" datasource="#application.dsn.platformStatsMC.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @actorMemberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.actorMemberID#">;
			DECLARE @memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">;

			SELECT subXML
			FROM dbo.sub_adminSubLog
			WHERE actorMemberID = @actorMemberID
			AND memberID = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif NOT len(local.qryLookupXML.subXML)>
			<cfset local.subXML = initSubXML(memberID=arguments.memberID)>

			<cfquery name="local.qryInsertXML" datasource="#application.dsn.platformStatsMC.dsn#">
				INSERT INTO dbo.sub_adminSubLog (actorMemberID, memberID, subXML)
				VALUES (
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.actorMemberID#">,
					<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,
					<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.subXML#">
				)
			</cfquery>
		<cfelse>
			<cfset local.subXML = local.qryLookupXML.subXML>
		</cfif>

		<cfreturn XMLParse(local.subXML)>
	</cffunction>

	<cffunction name="resetSubXML" access="private" output="false" returntype="xml">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.subXML = initSubXML(memberID=arguments.memberID)>
		<cfset saveSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.memberID, subXML=local.subXML)>

		<cfreturn xmlParse(local.subXML)>
	</cffunction>

	<cffunction name="initSubXML" access="private" output="false" returntype="string">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.firstUID = createUUID()>
		<cfset local.xmlSubscribeMember = '<sub><process memberid="#arguments.memberID#" pointer="#local.firstUID#" useRenewalRates="false" /><set uid="#local.firstUID#" id="0" /></sub>'>

		<cfreturn local.xmlSubscribeMember>
	</cffunction>

	<cffunction name="saveSubXML" access="private" output="false" returntype="void">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subXML" type="string" required="true">

		<cfset var qryUpdateXML = "">
		
		<cfquery name="qryUpdateXML" datasource="#application.dsn.platformStatsMC.dsn#">
			UPDATE dbo.sub_adminSubLog
			SET subXML = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.subXML#">
			WHERE actorMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.actorMemberID#">
			AND memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
		</cfquery>
	</cffunction>

	<cffunction name="removeSubXML" access="private" output="false" returntype="void">
		<cfargument name="actorMemberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryRemoveXML = "">
		
		<cfquery name="qryRemoveXML" datasource="#application.dsn.platformStatsMC.dsn#">
			DELETE FROM dbo.sub_adminSubLog
			WHERE actorMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.actorMemberID#">
			AND memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
		</cfquery>
	</cffunction>

	<!--- Coupon Functions --->
	<cffunction name="subCouponHandler" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "offercoupon":false, "couponapplied":false }>
		
		<cftry>
			<cfset local.xmlSubscribeMember = loadSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.memberID)>
			<cfset local.rootSubscriptionID = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@id)")>
			<cfset local.couponKey = "subCoupon_#arguments.memberID#_#local.rootSubscriptionID#">
			<cfset local.arrSubs = getCouponQualifiedSubs(xmlSubscribeMember=local.xmlSubscribeMember)>
			<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname=local.couponKey, defaultValue={})>

			<!--- coupon applied --->
			<cfif structCount(local.strCoupon) AND val(local.strCoupon.couponID)>
				<cfset local.encSubs = hash(serializeJSON(local.arrSubs), "SHA", "UTF-8")>

				<!--- re-apply coupon to subs --->
				<cfif local.encSubs NEQ local.strCoupon.encsubs>
					<cfset local.applyResult = validateCouponCode(mcproxy_siteID=arguments.mcproxy_siteID, couponCode=local.strCoupon.couponCode, memberID=arguments.memberID, arrSubs=local.arrSubs)>
				
					<cfset local.data.couponapplied = local.applyResult.isvalidcoupon>
					<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname=local.couponKey, defaultValue={})>
				<cfelse>
					<cfset local.data.couponapplied = true>
				</cfif>

				<cfif local.data.couponapplied>
					<cfset structInsert(local.data, "redeemdetail", local.strCoupon.redeemdetail)>
					<cfset structInsert(local.data, "discount", local.strCoupon.discount)>
					<cfset structInsert(local.data, "discountappliedtotal", local.strCoupon.discountappliedtotal)>
					<cfset structInsert(local.data, "totalamt", local.strCoupon.totalamt)>
				</cfif>
			
			<!--- offer coupon --->
			<cfelse>
				<cfset local.data.offercoupon = offerCoupon(siteID=arguments.mcproxy_siteID, memberID=arguments.memberID, arrSubs=local.arrSubs)>
			</cfif>

			<cfset local.data.success = true>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="offerCoupon" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="arrSubs" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.offercoupon = false>

		<cfif NOT arrayLen(arguments.arrSubs)>
			<cfreturn local.offercoupon>
		</cfif>
		
		<cftry>
			<cfxml variable="local.cartItemsXML">
				<cfoutput>
					<cart>
						<cfloop array="#arguments.arrSubs#" index="local.thisSub">
							<item mid="#arguments.memberID#" rateid="#local.thisSub.rateID#" itemid="#local.thisSub.subscriptionID#" itemtype="subscription" />
						</cfloop>
					</cart>
				</cfoutput>
			</cfxml>

			<!--- remove the <xml> tag, specifically the encoding. --->
			<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="Subscriptions">
				<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR " value="#local.cartItemsXML#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="1">
				<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.offerCoupon">
			</cfstoredproc>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.offercoupon = false>
		</cfcatch>
		</cftry>

		<cfreturn local.offercoupon>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="arrSubs" type="array" required="false">

		<cfscript>
		var local = structNew();
		local.returnStruct = { "success": false, "isvalidcoupon": false, "couponresponse": "Invalid Promo Code" };
		
		try {
			local.xmlSubscribeMember = loadSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.memberID);
			local.rootSubscriptionID = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@id)");
			local.couponKey = "subCoupon_#arguments.memberID#_#local.rootSubscriptionID#";

			if (arguments.keyExists("arrSubs")) {
				local.arrSubs = arguments.arrSubs;
			} else {
				local.arrSubs = getCouponQualifiedSubs(xmlSubscribeMember=local.xmlSubscribeMember);
			}
			
			arguments.couponCode = trim(arguments.couponCode);

			// reset sub coupon
			application.mcCacheManager.sessionSetValue(keyname=local.couponKey, value={});
			
			// if no length 
			if (len(arguments.couponCode) is 0) return local.returnStruct;

			var memberID = arguments.memberID;

			cfxml(variable="local.cartItemsXML") {
				writeOutput('<cart>');
				local.arrSubs.each(function(element, index) {
					writeOutput('<item mid="#memberID#" rateid="#arguments.element.rateid#" itemid="#arguments.element.subscriptionid#" parentitemid="#arguments.element.parentsubscriptionid#" itemtype="subscription" />');
				});
				writeOutput('</cart>');
			}

			local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','');

			var sqlParams = {
				siteID = { value=arguments.mcproxy_siteID, cfsqltype="CF_SQL_INTEGER" },
				applicationType = { value="Subscriptions", cfsqltype="CF_SQL_VARCHAR" },
				cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
				couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
			};

			var qryValidCoupon = queryExecute("
				SET NOCOUNT ON;
				
				DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
					@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml,
					@isAvailable bit;
				
				EXEC dbo.tr_isValidCouponCodeForAdmin @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
					@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
					@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT, @isAvailable=@isAvailable OUTPUT;
				
				SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML;
				", 
				sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
			);

			local.couponID = val(qryValidCoupon.couponID);
			local.returnStruct.couponResponse = qryValidCoupon.couponMessage;
			local.qualifiedCartItemsXML = qryValidCoupon.qualifiedCartItemsXML;

			// valid coupon
			if (local.couponID) {
				local.returnStruct.isValidCoupon = applyCouponToSubs(siteID=arguments.mcproxy_siteID, arrSubs=local.arrSubs, couponID=local.couponID, couponKey=local.couponKey,
													qualifiedCartItemsXML=local.qualifiedCartItemsXML);
			}

			local.returnStruct.success = true;
		} catch(any e) {
			application.objError.sendError(cfcatch=e, objectToDump=local);
			local.returnStruct.success = false;
		}
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="applyCouponToSubs" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="arrSubs" type="array" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="couponKey" type="string" required="true">
		<cfargument name="qualifiedCartItemsXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.siteID, couponID=arguments.couponID)>
		<cfset local.strCoupon = {}>

		<cfif local.qryCoupon.recordCount AND arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item"))>
			<cfset local.applyTo = XMLSearch(local.qryCoupon.subscriptionsXML,"string(/cs/applyto)")>

			<cfset local.strCoupon = { 
				"couponID": local.qryCoupon.couponID,
				"couponCode": local.qryCoupon.couponCode,
				"applyto": local.applyTo,
				"pctOff": local.qryCoupon.pctOff,
				"pctOffMaxOff": local.qryCoupon.pctOffMaxOff,
				"amtOff": local.qryCoupon.amtOff,
				"redeemDetail": local.qryCoupon.redeemDetail,
				"invoiceDetail": local.qryCoupon.invoiceDetail,
				"encsubs": hash(serializeJSON(arguments.arrSubs), "SHA", "UTF-8"),
				"qcixml": arguments.qualifiedCartItemsXML,
				"strsubprice": structNew("ordered"),
				"totalamt": 0,
				"discount": 0,
				"discountappliedtotal": 0
			}>

			<cfloop array="#arguments.arrSubs#" index="local.thisSub">
				<cfset local.strCoupon.totalamt = NumberFormat(precisionEvaluate(local.strCoupon.totalamt + local.thisSub.amt),"0.00")>
			</cfloop>

			<cfset local.maxDiscountAmt = val(local.qryCoupon.amtOff) GT 0 ? val(local.qryCoupon.amtOff) : val(local.qryCoupon.pctOffMaxOff)>
			<cfset local.maxDiscountAmtRemaining = local.maxDiscountAmt>

			<cfloop array="#arguments.arrSubs#" index="local.thisSub">
				<cfset local.discount = 0>
				
				<cfset local.thisCartItemQualified = arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item[@itemid='#local.thisSub.subscriptionid#'][@rateid='#local.thisSub.rateid#']"))>
				<cfif local.thisCartItemQualified AND local.thisSub.amt>
					<!--- Percentage Off --->
					<cfif val(local.qryCoupon.pctOff) gt 0>
						<cfset local.discount = NumberFormat(precisionEvaluate(local.thisSub.amt * (local.qryCoupon.pctOff / 100)),"0.00")>
						
						<!--- Maximum Discount Amount --->
						<cfif val(local.qryCoupon.pctOffMaxOff)>
							<cfif local.applyTo EQ 'subtree'>
								<cfset local.discount = MIN(local.discount,local.maxDiscountAmtRemaining)>
								<cfset local.maxDiscountAmtRemaining = NumberFormat(precisionEvaluate(local.maxDiscountAmtRemaining - local.discount),"0.00")>
							<cfelseif local.applyTo EQ 'sub' AND MAX(local.qryCoupon.pctOffMaxOff,local.discount) EQ local.discount>
								<cfset local.discount = local.qryCoupon.pctOffMaxOff>
							</cfif>
						</cfif>

						<cfif val(local.qryCoupon.pctOffMaxOff) gt 0 and max(local.qryCoupon.pctOffMaxOff,local.discount) EQ local.discount>
							<cfset local.discount = local.qryCoupon.pctOffMaxOff>
						</cfif>

					<!--- Dollar Amount Off --->
					<cfelseif val(local.qryCoupon.amtOff) gt 0>
						<cfif local.applyTo EQ 'subtree'>
							<cfset local.discount = min(local.thisSub.amt,local.maxDiscountAmtRemaining)>
							<cfset local.maxDiscountAmtRemaining = NumberFormat(precisionEvaluate(local.maxDiscountAmtRemaining - local.discount),"0.00")>
						<cfelse>
							<cfset local.discount = min(local.thisSub.amt,local.qryCoupon.amtOff)>
						</cfif>
					</cfif>
				</cfif>

				<cfset local.strCoupon.strSubPrice[local.thisSub.subscriptionid] = { 
					"rateid": local.thisSub.rateid,
					"amt":local.thisSub.amt, 
					"discount":local.discount, 
					"amtafterdiscount": NumberFormat(precisionEvaluate(local.thisSub.amt - local.discount),"0.00"),
					"numinstallments": local.thisSub.numinstallments, 
					"discountperinstallment": local.discount GT 0 ? NumberFormat(precisionEvaluate(local.discount / local.thisSub.numinstallments),"0.00") : 0
				}>

				<cfset local.strCoupon.discount = NumberFormat(precisionEvaluate(local.strCoupon.discount + local.discount),"0.00")>

				<cfif local.applyTo EQ 'subtree' AND local.maxDiscountAmt GT 0 AND local.maxDiscountAmtRemaining EQ 0>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfset local.strCoupon.discountappliedtotal = NumberFormat(precisionEvaluate(local.strCoupon.totalamt - local.strCoupon.discount),"0.00")>
			<cfset local.success = true>
		</cfif>

		<cfset application.mcCacheManager.sessionSetValue(keyname=arguments.couponKey, value=local.strCoupon)>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="removeAppliedCoupon" access="public" output="false" returntype="struct">
		<cfargument name="memberID" type="numeric" required="true">
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { "success" = false };

		local.xmlSubscribeMember = loadSubXML(actorMemberID=session.cfcuser.memberdata.memberID, memberID=arguments.memberID);
		local.rootSubscriptionID = xmlSearch(local.xmlSubscribeMember,"string(//set[@id='0']/subscription/@id)");
		local.couponKey = "subCoupon_#arguments.memberID#_#local.rootSubscriptionID#";
		
		// remove applied coupon
		application.mcCacheManager.sessionSetValue(keyname=local.couponKey, value={});

		local.returnStruct.success = true;
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="getCouponQualifiedSubs" access="public" output="false" returntype="array">
		<cfargument name="xmlSubscribeMember" type="xml" required="true">
		
		<cfset var local = structNew()>
		<cfset local.arrSubs = []>
		
		<cfset local.rateInstallments = val(xmlSearch(arguments.xmlSubscribeMember,"string(//set[@id=0]/subscription/@rateInstallments)"))>
		
		<cfloop array="#XMLSearch(arguments.xmlSubscribeMember,'//subscription')#" index="local.thisSub">
			<cfset local.saveRateToUse = getRateToUse(useRates=local.thisSub.xmlAttributes.useRates, rateAmt=local.thisSub.xmlAttributes.rateAmt, 
						rateInstallments=local.thisSub.xmlAttributes.rateInstallments, numPaymentsToUse=local.rateInstallments, 
						pcPctOff=local.thisSub.xmlAttributes.pcpctoff, pcRateAmt=local.thisSub.xmlAttributes.pcrateamt)>

			<cfset local.setID = xmlSearch(local.thisSub,'number(../@id)')>
			<cfset local.parentSubscriptionID = local.setID GT 0 ? xmlSearch(local.thisSub,'number(../../@id)') : 0>

			<cfset local.arrSubs.append({
				"subscriptionID": local.thisSub.xmlAttributes.id,
				"parentSubscriptionID": local.parentSubscriptionID,
				"rateID": local.thisSub.xmlAttributes.rateID,
				"numinstallments": local.rateInstallments,
				"amt": val(local.saveRateToUse.rateTotal)
			})>
		</cfloop>

		<cfreturn local.arrSubs>
	</cffunction>

</cfcomponent>