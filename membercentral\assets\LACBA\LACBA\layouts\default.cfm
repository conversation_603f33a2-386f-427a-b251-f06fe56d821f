<cfoutput>

<cfset local.isSideEnabled = false />
<cfset local.headerBackground = ""/>
<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['L'],1)>
		<cfif event.getValue("mc_pageDefinition").pageZones['L'][1].RESOURCENODEATTRIBUTES.assignmentLevel EQ 'page'>
			<cfset local.headerBackground =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['L'][1].data,"<p>",""),"</p>",""))/>
		<cfelse>
			<cfset local.lastElement = arrayLen(event.getValue("mc_pageDefinition").pageZones['L'])>
			<cfset local.headerBackground = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['L'][local.lastElement].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
</cfif>
<cfif len(local.headerBackground) eq 0 and application.objCMS.getZoneItemCount(zone='K',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['K'],1)>
		<cfset local.headerBackground = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['K'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>
<cfif len(local.headerBackground) eq 0>
	<cfset local.headerBackground = '<img src="/images/banner_image.jpg"/>'>
</cfif>

<cfset local.zoneM1Content = ""/>
<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['M'],1)>
		<cfset local.zoneM1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['M'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>

<cfset local.zonePContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='P',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['P'],1)>
		<cfif event.getValue("mc_pageDefinition").pageZones['P'][1].RESOURCENODEATTRIBUTES.assignmentLevel EQ 'page'>
			<cfset local.zonePContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['P'][1].data,"<p>",""),"</p>",""))/>
		<cfelse>
			<cfset local.lastElement = arrayLen(event.getValue("mc_pageDefinition").pageZones['P'])>
			<cfset local.zonePContent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['P'][local.lastElement].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
</cfif>

<cfset local.zoneQContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
		<cfif event.getValue("mc_pageDefinition").pageZones['Q'][1].RESOURCENODEATTRIBUTES.assignmentLevel EQ 'page'>
			<cfset local.zoneQContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['Q'][1].data,"<p>",""),"</p>",""))/>
		<cfelse>
			<cfset local.lastElement = arrayLen(event.getValue("mc_pageDefinition").pageZones['Q'])>
			<cfset local.zoneQContent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['Q'][local.lastElement].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
</cfif>

<cfset local.zoneWContent = ""/>
<cfif application.objCMS.getZoneItemCount(zone='W',event=event)>
	<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['W'],1)>
		<cfset local.zoneWContent =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['W'][1].data,"<p>",""),"</p>",""))/>
	</cfif>
</cfif>

<cfif len(trim(local.zonePContent)) OR len(trim(local.zoneQContent)) OR len(trim(local.zoneWContent))>
	<cfset local.isSideEnabled = true />
</cfif>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
	<head>
		<cfinclude template="head.cfm">
	</head>
	<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		<body class="single-default">
			<div id="preloader"></div>
			<cfinclude template="header.cfm">
			<span class="headerBackgroundWrapper hide">#local.headerBackground#</span>
			<span class="headerContentWrapper hide">#local.zoneM1Content#</span>
			<div id="banner">
				<div class="banner-image aos-item headerBackgroundHolder" data-aos="fade-in"></div>
				<div class="container">
					<div class="row-fluid">
						<div class="col span7 animated fadeInLeft delay-1">
							<div class="breadcrumbHolder hide">
								<div id="breadcrumbs">
									<cfloop from="#event.getValue('mc_pageDefinition.qrySectionTreeUp').recordcount#" to="1" step="-1" index="idx"> 
										<cfset local.sectionName = event.getValue('mc_pageDefinition.qrySectionTreeUp').sectionName[idx]/>
										<cfset local.sectionBreadcrumb = event.getValue('mc_pageDefinition.qrySectionTreeUp').sectionBreadcrumb[idx]/>
										<cfoutput><a href='<cfif local.sectionName eq "Root">/<cfelse><cfif len(local.sectionBreadcrumb)>/?pg=#local.sectionBreadcrumb#<cfelse>javascript:void(0);</cfif></cfif>'><cfif local.sectionName eq "Root">Home<cfelse>#local.sectionName#</cfif></a><span> - </span></cfoutput>
									</cfloop>
									#event.getValue('mc_pageDefinition.pageTitle')#
								</div>
							</div>
							<h1 class="hide headTitle">#event.getValue('mc_pageDefinition.pageTitle')#</h1>
							<span class="headerContentHolder"></span>
						</div>
					</div>
				</div>
			</div>
			<main>
				<div class="container">
					<div id="container-area">
						<div id="content" class="aos-item" data-aos="fade-down" <cfif local.isSideEnabled and event.getValue('mc_pageDefinition.layoutMode','normal') neq "full"> style="width:65.56%" <cfelse> style="width:100%!important" </cfif>>
							<div class="contentBx innerPage-content">
								<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
									#application.objCMS.renderZone(zone='Main',event=event)#
								</cfif>
							</div>
						</div>
						<span class="sidebarAlertWrapper hide">#local.zoneQContent#</span>
						<span class="sidebarAdWrapper hide">#local.zoneWContent#</span>
						<cfif local.isSideEnabled and event.getValue('mc_pageDefinition.layoutMode','normal') neq "full">
							<div id="sidebar" class="aos-item" data-aos="fade-down">
								<cfif len(trim(local.zonePContent))>
									<div class="sidebar-widget sidebarNavLinks">
										#local.zonePContent#
									</div>
								</cfif>
								<cfif len(trim(local.zoneQContent))>
									<div class="sidebar-widget">
										<div class="sidebar-alert sidebarAlertHolder">
										</div>
									</div>
								</cfif>
								<cfif len(trim(local.zoneWContent))>
									<div class="sidebar-widget">
										<div class="sidebar-ad">
										</div>
									</div>
								</cfif>
							</div>
						</cfif>
					</div>
				</div>
			</main>

			<cfif len(trim(local.zoneDContent)) OR len(trim(local.zoneUpcommingContent))>
				<div id="bottom-posts">
					<div class="container">
						<div id="latest-news">
							<span class="blogHolder"></span>
						</div> 
						<div id="upcoming-events" class="upcomingEvents">
							<cfif len(trim(local.zoneUpcommingContent))>
								<span class="eventHolder"></span>
							</cfif>
						</div>
					</div>
				</div>
			</cfif>
			<cfif len(trim(local.zoneFContent))>
				<div id="bottom-ad">
					<div id="bottom-ad-bx">
						#local.zoneFContent#
					</div>
				</div>
			</cfif>
			<cfif len(trim(local.sponsorContent)) OR len(trim(local.sponsorAdContent))>
				<span class="sponsorHolder"></span>
			</cfif>
			<cfinclude template="footer.cfm">
		</body>
	<cfelse>
		<body class="innerPage-content">
			<cfif application.objCMS.getZoneItemCount(zone='Main',event=event)>
				#application.objCMS.renderZone(zone='Main',event=event)#
			</cfif>
		</body>
	</cfif>
	<cfinclude template="foot.cfm">
	<cfinclude template="toolBar.cfm">
</html>
</cfoutput>